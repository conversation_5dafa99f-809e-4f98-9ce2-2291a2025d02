export const useDynamicFontOptimization = () => {
    // Dynamic font optimization composable
    
    const detectAndPreloadFonts = () => {
        if (!import.meta.client) return []
        
        console.log('🔤 Starting dynamic font detection...')
        
        // Method 1: Scan performance entries for loaded fonts
        const detectFromPerformanceAPI = () => {
            const fontFiles = new Set<string>()
            
            if ('performance' in window && performance.getEntriesByType) {
                const resourceEntries = performance.getEntriesByType('resource')
                
                resourceEntries.forEach((entry: any) => {
                    if (entry.name.includes('.woff2') || entry.name.includes('.woff')) {
                        try {
                            const url = new URL(entry.name)
                            const pathname = url.pathname
                            
                            // Check if it's a Nuxt-generated font or Montserrat
                            if (pathname.includes('/_nuxt/') || pathname.includes('Montserrat')) {
                                fontFiles.add(pathname)
                                console.log('🔤 Detected font from performance API:', pathname)
                            }
                        } catch (e) {
                            // Invalid URL, skip
                        }
                    }
                })
            }
            
            return Array.from(fontFiles)
        }
        
        // Method 2: Scan existing link tags for font patterns
        const detectFromExistingLinks = () => {
            const fontFiles = new Set<string>()
            
            // Check existing preload links
            const preloadLinks = document.querySelectorAll('link[rel="preload"][as="font"]')
            preloadLinks.forEach(link => {
                const href = link.getAttribute('href')
                if (href && (href.includes('/_nuxt/') || href.includes('Montserrat'))) {
                    fontFiles.add(href)
                    console.log('🔤 Found existing font preload:', href)
                }
            })
            
            // Check stylesheet links that might contain fonts
            const stylesheetLinks = document.querySelectorAll('link[rel="stylesheet"]')
            stylesheetLinks.forEach(link => {
                const href = link.getAttribute('href')
                if (href && href.includes('/_nuxt/')) {
                    // This might be a CSS file containing font references
                    console.log('🔤 Found potential font-containing CSS:', href)
                }
            })
            
            return Array.from(fontFiles)
        }
        
        // Method 3: Use MutationObserver to detect dynamically added fonts
        const observeFontAdditions = () => {
            const fontFiles = new Set<string>()
            
            const observer = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            const element = node as Element
                            
                            // Check if it's a font preload link
                            if (element.tagName === 'LINK' && 
                                element.getAttribute('rel') === 'preload' &&
                                element.getAttribute('as') === 'font') {
                                
                                const href = element.getAttribute('href')
                                if (href && (href.includes('/_nuxt/') || href.includes('Montserrat'))) {
                                    fontFiles.add(href)
                                    console.log('🔤 Detected dynamically added font:', href)
                                }
                            }
                            
                            // Check for style elements with font-face
                            if (element.tagName === 'STYLE') {
                                const content = element.textContent || ''
                                const fontUrlRegex = /url\(['"]?([^'"]*\.woff2?)['"]*\)/gi
                                let match
                                
                                while ((match = fontUrlRegex.exec(content)) !== null) {
                                    if (match[1].includes('/_nuxt/') || match[1].includes('Montserrat')) {
                                        fontFiles.add(match[1])
                                        console.log('🔤 Detected font from style element:', match[1])
                                    }
                                }
                            }
                        }
                    })
                })
            })
            
            // Start observing
            observer.observe(document.head, {
                childList: true,
                subtree: true
            })
            
            // Stop observing after 5 seconds
            setTimeout(() => {
                observer.disconnect()
                console.log('🔤 Font detection observer stopped')
            }, 5000)
            
            return Array.from(fontFiles)
        }
        
        // Method 4: Scan document for font-family declarations
        const detectFromComputedStyles = () => {
            const fontFamilies = new Set<string>()
            
            // Check body and common elements for font-family
            const elementsToCheck = [document.body, document.documentElement]
            const commonSelectors = ['h1', 'h2', 'h3', 'p', '.font-montserrat', '[class*="font"]']
            
            commonSelectors.forEach(selector => {
                const elements = document.querySelectorAll(selector)
                elements.forEach(el => elementsToCheck.push(el as HTMLElement))
            })
            
            elementsToCheck.forEach(element => {
                if (element) {
                    const computedStyle = window.getComputedStyle(element)
                    const fontFamily = computedStyle.fontFamily
                    
                    if (fontFamily && fontFamily.includes('Montserrat')) {
                        fontFamilies.add('Montserrat')
                        console.log('🔤 Detected Montserrat usage in:', element.tagName || 'unknown')
                    }
                }
            })
            
            return Array.from(fontFamilies)
        }
        
        // Combine all detection methods
        const allDetectedFonts = [
            ...detectFromPerformanceAPI(),
            ...detectFromExistingLinks(),
            ...observeFontAdditions()
        ]
        
        const detectedFamilies = detectFromComputedStyles()
        
        // Remove duplicates
        const uniqueFonts = [...new Set(allDetectedFonts)]
        
        console.log('🔤 All detected fonts:', uniqueFonts)
        console.log('🔤 Detected font families:', detectedFamilies)
        
        return {
            fontFiles: uniqueFonts,
            fontFamilies: detectedFamilies
        }
    }
    
    const preloadDetectedFonts = (fontFiles: string[]) => {
        if (!import.meta.client) return
        
        fontFiles.forEach(fontPath => {
            // Skip if already preloaded
            if (document.querySelector(`link[href="${fontPath}"][rel="preload"]`)) {
                return
            }
            
            const link = document.createElement('link')
            link.rel = 'preload'
            link.as = 'font'
            link.type = 'font/woff2'
            link.href = fontPath
            link.crossOrigin = 'anonymous'
            link.setAttribute('fetchpriority', 'high')
            link.setAttribute('data-dynamic-font', 'composable')
            
            // Insert at the beginning of head for highest priority
            document.head.insertBefore(link, document.head.firstChild)
            console.log('🔤 Preloaded detected font:', fontPath)
        })
    }
    
    const addFontDisplaySwap = () => {
        if (!import.meta.client) return
        
        // Add font-display: swap for better loading performance
        const style = document.createElement('style')
        style.setAttribute('data-font-optimization', 'composable')
        style.textContent = `
            /* Dynamic font optimization */
            @font-face {
                font-family: 'Montserrat';
                font-display: swap;
            }
            
            /* Fallback font stack */
            body, .font-montserrat {
                font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                font-display: swap;
            }
            
            /* Prevent FOIT (Flash of Invisible Text) */
            .font-loading {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }
        `
        
        document.head.appendChild(style)
        console.log('🔤 Added font-display: swap optimization')
    }
    
    const optimizeFonts = () => {
        if (!import.meta.client) return
        
        // Run detection
        const detected = detectAndPreloadFonts()
        
        // Preload detected fonts
        if (detected.fontFiles.length > 0) {
            preloadDetectedFonts(detected.fontFiles)
        }
        
        // Add font optimization CSS
        addFontDisplaySwap()
        
        // Return detection results
        return detected
    }
    
    return {
        detectAndPreloadFonts,
        preloadDetectedFonts,
        addFontDisplaySwap,
        optimizeFonts
    }
}
