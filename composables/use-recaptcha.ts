declare global {
  interface Window {
    grecaptcha?: {
      ready: (callback: () => void) => void
      execute: (siteKey: string, options: { action: string }) => Promise<string>
      render: (container: string | HTMLElement, options: any) => number
      reset: (widgetId: number) => void
      getResponse: (widgetId: number) => string
    }
    onRecaptchaLoad?: () => void
  }
}

interface RecaptchaConfig {
  v3SiteKey: string
  v2SiteKey: string
  v3Action: string
  timeout: number
}

export const useRecaptcha = () => {
  const config = useRuntimeConfig()
  const isLoaded = ref(false)
  const isLoading = ref(false)
  const isV2Visible = ref(false)
  const v2WidgetId = ref<number | null>(null)
  const v2ContainerId = ref<string>('')
  const isV2Error = ref(false)

  const recaptchaToken = ref<string>('')
  const RECAPTCHA_VERSION = {
    V2: 'v2',
    V3: 'v3'
  }
  const currentVersion = ref<string>('')

  const loadingPromises = new Map<string, Promise<void>>()
  const { recaptchaV3SiteKey, recaptchaV2SiteKey } = useRuntimeConfig().public
  // Default configuration
  const defaultConfig: RecaptchaConfig = {
    v3SiteKey: (recaptchaV3SiteKey as string) || '',
    v2SiteKey: (recaptchaV2SiteKey as string) || '',
    v3Action: 'submit',
    timeout: 10000
  }

  const loadRecaptcha = (version: 'v3' | 'v2' = 'v3'): Promise<void> => {
    const cacheKey = `recaptcha-${version}`

    if (isLoading.value && loadingPromises.has(cacheKey)) {
      return loadingPromises.get(cacheKey)!
    }

    const loadPromise = new Promise<void>((resolve, reject) => {
      isLoading.value = true

      const scriptSrc = version === 'v3' 
        ? `https://www.google.com/recaptcha/api.js?render=${defaultConfig.v3SiteKey}`
        : `https://www.google.com/recaptcha/api.js?onload=onRecaptchaLoad&render=explicit`

      const existingScript = document.querySelector(
        `script[src*="google.com/recaptcha/api.js"]`
      )

      if (existingScript) {
        const checkGrecaptcha = () => {
          if (window.grecaptcha) {
            isLoaded.value = true
            isLoading.value = false
            loadingPromises.delete(cacheKey)
            resolve()
          } else {
            setTimeout(checkGrecaptcha, 100)
          }
        }
        checkGrecaptcha()
        return
      }

      // Handle v2 onload callback
      if (version === 'v2') {
        window.onRecaptchaLoad = () => {
          isLoaded.value = true
          isLoading.value = false
          loadingPromises.delete(cacheKey)
          resolve()
        }
      }

      const script = document.createElement('script')
      script.src = scriptSrc
      script.async = true
      script.defer = true

      script.onload = () => {
        if (version === 'v3') {
          const checkGrecaptcha = () => {
            if (window.grecaptcha) {
              isLoaded.value = true
              isLoading.value = false
              loadingPromises.delete(cacheKey)
              resolve()
            } else {
              setTimeout(checkGrecaptcha, 100)
            }
          }
          checkGrecaptcha()
        }
      }

      script.onerror = (error) => {
        isLoading.value = false
        loadingPromises.delete(cacheKey)
        reject(new Error(`Failed to load reCAPTCHA ${version} script`))
      }

      document.head.appendChild(script)
    })

    loadingPromises.set(cacheKey, loadPromise)
    return loadPromise
  }

  const executeRecaptchaV3 = async (action: string = 'submit', timeout: number = 10000): Promise<string> => {
    try {
      await loadRecaptcha('v3')

      return new Promise((resolve, reject) => {
        const timeoutId = setTimeout(() => {
          reject(new Error('reCAPTCHA v3 execution timeout'))
        }, timeout)

        if (!window.grecaptcha) {
          clearTimeout(timeoutId)
          reject(new Error('reCAPTCHA v3 not available'))
          return
        }

        try {
          window.grecaptcha.ready(() => {
            window.grecaptcha!
              .execute(defaultConfig.v3SiteKey, { action })
              .then((token: string) => {
                clearTimeout(timeoutId)
                if (!token) {
                  reject(new Error('Failed to get reCAPTCHA v3 token'))
                  return
                }
                recaptchaToken.value = token
                currentVersion.value = RECAPTCHA_VERSION.V3
                resolve(token)
              })
              .catch((error: any) => {
                clearTimeout(timeoutId)
                const errorMessage = error?.message || error || 'Unknown reCAPTCHA v3 error'
                reject(new Error(`reCAPTCHA v3 execution failed: ${errorMessage}`))
              })
          })
        } catch (error: any) {
          clearTimeout(timeoutId)
          reject(new Error(`reCAPTCHA v3 setup failed: ${error?.message || error}`))
        }
      })
    } catch (error) {
      throw error
    }
  }

  const renderRecaptchaV2 = (containerId: string): Promise<string> => {
    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        reject(new Error('reCAPTCHA v2 render timeout'))
      }, defaultConfig.timeout)

      try {
        loadRecaptcha('v2').then(() => {
          if (!window.grecaptcha) {
            clearTimeout(timeoutId)
            reject(new Error('reCAPTCHA v2 not available'))
            return
          }

          v2ContainerId.value = containerId
          isV2Visible.value = true

          // Ensure container exists and is visible
          const container = document.getElementById(containerId)
          if (!container) {
            clearTimeout(timeoutId)
            reject(new Error('reCAPTCHA v2 container not found'))
            return
          }

          // Make sure container is visible
          container.style.display = 'block'
          container.style.visibility = 'visible'

          const widgetId = window.grecaptcha.render(containerId, {
            sitekey: defaultConfig.v2SiteKey,
            callback: (token: string) => {
              clearTimeout(timeoutId)
              if (token) {
                recaptchaToken.value = token
                currentVersion.value = RECAPTCHA_VERSION.V2
                resolve(token)
              } else {
                reject(new Error('Failed to get reCAPTCHA v2 token'))
              }
            },
            'expired-callback': () => {
              clearTimeout(timeoutId)
              reject(new Error('reCAPTCHA v2 expired'))
            },
            'error-callback': () => {
              clearTimeout(timeoutId)
              reject(new Error('reCAPTCHA v2 error'))
            },
            theme: 'light',
            size: 'normal',
            badge: 'bottomright'
          })

          v2WidgetId.value = widgetId
        }).catch((error) => {
          clearTimeout(timeoutId)
          reject(error)
        })
      } catch (error: any) {
        clearTimeout(timeoutId)
        reject(new Error(`reCAPTCHA v2 setup failed: ${error?.message || error}`))
      }
    })
  }

  const resetRecaptchaV2 = () => {
    if (v2WidgetId.value && window.grecaptcha) {
      window.grecaptcha.reset(v2WidgetId.value)
    }
  }

  const hideRecaptchaV2 = () => {
    isV2Visible.value = false
    v2WidgetId.value = null
    v2ContainerId.value = ''
  }

  const getV2Token = async (action: string = 'submit'): Promise<string> => {
    try {
      if (recaptchaV2SiteKey) {
        // Use existing container in popup instead of creating new one
        const containerId = 'recaptcha-v2-container'

        // Render v2 in the existing container
        const token = await renderRecaptchaV2InContainer(containerId)
        const container = document.getElementById(containerId)
        if (container) {
          container.style.display = 'flex'
        }
        recaptchaToken.value = token
        currentVersion.value = RECAPTCHA_VERSION.V2
        return token
      }
      isV2Error.value = true
      return ''
    } catch (v2Error) {
      // isV2Error.value = true
      // Return empty token to allow bypass
      recaptchaToken.value = ''
      currentVersion.value = ''
      return ''
    }
  }

  const executeRecaptchaWithFallback = async (action: string = 'submit'): Promise<string> => {
    try {
      if (recaptchaV3SiteKey) {
        // Try v3 first
        const token = await executeRecaptchaV3(action)
        return token
      }
      currentVersion.value = RECAPTCHA_VERSION.V2
      return await getV2Token()
    } catch (v3Error) {
      currentVersion.value = RECAPTCHA_VERSION.V2
      return await getV2Token()
    }
  }

  const executeRecaptcha = async (action: string = 'submit'): Promise<string> => {
    try {
      if (recaptchaV3SiteKey || recaptchaV2SiteKey) {
        return await executeRecaptchaWithFallback(action)
      }
      return ''
    } catch (error) {
      // Return empty token to allow bypass
      return ''
    }
  }

  // New function to render v2 in existing container
  const renderRecaptchaV2InContainer = (containerId: string): Promise<string> => {
    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        reject(new Error('reCAPTCHA v2 render timeout'))
      }, defaultConfig.timeout)

      try {
        loadRecaptcha('v2').then(() => {
          if (!window.grecaptcha) {
            clearTimeout(timeoutId)
            isV2Error.value = true
            reject(new Error('reCAPTCHA v2 not available'))
            return
          }

          v2ContainerId.value = containerId
          isV2Visible.value = true

          // Ensure container exists and is visible
          const container = document.getElementById(containerId)
          if (!container) {
            clearTimeout(timeoutId)
            // reject(new Error('reCAPTCHA v2 container not found'))
            return
          }

          // Make sure container is visible
          container.style.display = 'block'
          container.style.visibility = 'visible'

          const widgetId = window.grecaptcha.render(containerId, {
            sitekey: defaultConfig.v2SiteKey,
            callback: (token: string) => {
              clearTimeout(timeoutId)
              if (token) {
                // Hide v2 after successful verification
                hideRecaptchaV2()
                recaptchaToken.value = token
                currentVersion.value = RECAPTCHA_VERSION.V2
                resolve(token)
              } else {
                isV2Error.value = true
                reject(new Error('Failed to get reCAPTCHA v2 token'))
              }
            },
            'expired-callback': () => {
              clearTimeout(timeoutId)
              hideRecaptchaV2()
              reject(new Error('reCAPTCHA v2 expired'))
            },
            'error-callback': () => {
              clearTimeout(timeoutId)
              hideRecaptchaV2()
              isV2Error.value = true
              reject(new Error('reCAPTCHA v2 error'))
            },
            theme: 'light',
            size: 'normal',
            badge: 'bottomright'
          })

          v2WidgetId.value = widgetId

        }).catch((error) => {
          isV2Error.value = true
          clearTimeout(timeoutId)
          reject(error)
        })
      } catch (error: any) {
        isV2Error.value = true
        clearTimeout(timeoutId)
        reject(new Error(`reCAPTCHA v2 setup failed: ${error?.message || error}`))
      }
    })
  }

  // Function to handle form submission after v2 verification
  const submitAfterV2Verification = async (formData: any, submitFunction: (data: any) => Promise<any>) => {
    try {
      if (isV2Visible.value) {
        // Wait for v2 verification
        const token = await renderRecaptchaV2InContainer(v2ContainerId.value)
        return await submitFunction({ ...formData, token })
      } else {
        // Use v3
        const token = await executeRecaptchaV3('login')
        return await submitFunction({ ...formData, token })
      }
    } catch (error) {
      throw error
    }
  }

  return {
    executeRecaptcha,
    executeRecaptchaV3,
    renderRecaptchaV2,
    renderRecaptchaV2InContainer,
    resetRecaptchaV2,
    hideRecaptchaV2,
    submitAfterV2Verification,
    getV2Token,
    RECAPTCHA_VERSION,
    currentVersion,
    recaptchaToken,
    isLoaded: readonly(isLoaded),
    isLoading: readonly(isLoading),
    isV2Visible: isV2Visible,
    v2ContainerId: readonly(v2ContainerId),
    isV2Error
  }
}
