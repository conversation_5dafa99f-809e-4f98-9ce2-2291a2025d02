// Minimal performance utilities - Ultra lightweight
export const useMinimalPerformance = () => {
    // Lightweight font preloading
    const preloadFonts = () => {
        if (!import.meta.client) return 0
        
        const fonts = new Set<string>()
        
        // Only check performance API - most efficient
        if (performance.getEntriesByType) {
            performance.getEntriesByType('resource').forEach((entry: any) => {
                if (entry.name.includes('.woff2') && entry.name.includes('/_nuxt/')) {
                    try {
                        fonts.add(new URL(entry.name).pathname)
                    } catch {}
                }
            })
        }
        
        // Preload with minimal DOM operations
        fonts.forEach(fontPath => {
            if (!document.querySelector(`link[href="${fontPath}"]`)) {
                const link = document.createElement('link')
                link.rel = 'preload'
                link.as = 'font'
                link.href = fontPath
                link.crossOrigin = 'anonymous'
                document.head.prepend(link)
            }
        })
        
        return fonts.size
    }
    
    // Lightweight image preloading
    const preloadLCPImage = () => {
        if (!import.meta.client) return
        
        const { isMobile } = useDevice()
        const staticUrl = useRuntimeConfig().public.staticUrl
        const imagePath = `${staticUrl}/home/<USER>/jackpot/${isMobile ? 'mb' : 'pc'}.avif`
        
        if (!document.querySelector(`link[href="${imagePath}"]`)) {
            const link = document.createElement('link')
            link.rel = 'preload'
            link.as = 'image'
            link.href = imagePath
            link.setAttribute('fetchpriority', 'high')
            document.head.prepend(link)
        }
    }
    
    // Minimal script optimization
    const optimizeScripts = () => {
        if (!import.meta.client) return 0
        
        let count = 0
        document.querySelectorAll('script[src]').forEach(script => {
            const src = script.getAttribute('src') || ''
            if (src.includes('analytics') || src.includes('gtm') || src.includes('facebook')) {
                if (!script.hasAttribute('defer')) {
                    script.setAttribute('defer', 'true')
                    script.setAttribute('fetchpriority', 'low')
                    count++
                }
            }
        })
        
        return count
    }
    
    // Minimal image lazy loading
    const optimizeImages = () => {
        if (!import.meta.client) return 0
        
        let count = 0
        document.querySelectorAll('img[src*="/providers/"]').forEach(img => {
            if (!img.hasAttribute('loading')) {
                img.setAttribute('loading', 'lazy')
                img.setAttribute('fetchpriority', 'low')
                count++
            }
        })
        
        return count
    }
    
    // Ultra minimal performance monitoring
    const monitorLCP = () => {
        if (!import.meta.client || !('PerformanceObserver' in window)) return
        
        const observer = new PerformanceObserver(list => {
            list.getEntries().forEach(entry => {
                if (entry.entryType === 'largest-contentful-paint') {
                    const lcp = entry.startTime
                    console.log(`⚡ LCP: ${lcp.toFixed(0)}ms ${lcp < 2500 ? '✅' : '⚠️'}`)
                }
            })
        })
        
        observer.observe({ entryTypes: ['largest-contentful-paint'] })
    }
    
    // All-in-one optimization function
    const optimize = () => {
        const fonts = preloadFonts()
        preloadLCPImage()
        const scripts = optimizeScripts()
        const images = optimizeImages()
        monitorLCP()
        
        return { fonts, scripts, images }
    }
    
    return {
        preloadFonts,
        preloadLCPImage,
        optimizeScripts,
        optimizeImages,
        monitorLCP,
        optimize
    }
}
