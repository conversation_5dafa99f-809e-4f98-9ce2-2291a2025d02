import { io } from 'socket.io-client'
import type { ResponseParamsNotification } from '~/interfaces/notification'
import { ref, onMounted, computed, onUnmounted } from 'vue'
import { jackpotConditions } from '~/constants/game'
import type { JackpotGame } from '~/interfaces/game'
import useCustomFetch from '~/composables/use-custom-fetch'
import { NO_DATA_VIEWER_LIVESTREAM_GAMES } from '~/resources/live-casino'

// Define interface for viewer data
interface ViewerData {
    gameId: string
    viewers: number
}

export const useSocket = defineStore(
    'socketStore',
    () => {
        const socket = ref<any>(null)
        const { isSafari } = useDevice()

        const isLostedInternet = ref(false)
        const fetcher = useCustomFetch()
        const notificationList = ref<ResponseParamsNotification[]>([])
        const jackpots = ref<{ [key: string]: string }>({})
        const prevJackpotGo88 = ref<JackpotGame[]>([] as JackpotGame[])
        const jackpotGo88 = ref<JackpotGame[]>([] as JackpotGame[])
        const jackpotGo88Popup = ref<JackpotGame[]>([] as JackpotGame[])
        const jackpotGo88Aviator = ref<JackpotGame[]>([] as JackpotGame[])
        const jackpotGo88AviatorPopup = ref<JackpotGame[]>([] as JackpotGame[])
        const previousJackpots = ref<{ [key: string]: string }>({})
        const totalJackpot = ref(0)
        const bigWinUser = ref<any>(null)
        const previousTotalJackpot = ref(0)
        const socketUrl = useRuntimeConfig().public.NUXT_PUBLIC_SOCKET_URL
        const useUserStoreInstance = useUserStore()
        const { user } = storeToRefs(useUserStoreInstance)
        const token = user.value?.token || ''
        const viewers = ref<ViewerData[]>([])
        const previousViewers = ref<ViewerData[]>([])
        const previousRikViewers = ref<{[key: string]: number}>({
            'rik_vgmn_108': 0,
            'rik_vgmn_109': 0,
            'rik_vgmn_110': 0,
            'rik_vgmn_111': 0,
            '789club_G1X_305': 0,
            '789club_G1X_306': 0,
            'sunwin_G1S_305': 0,
            'sunwin_G1X_306': 0,
            'sunwin_G1S_311': 0,
            'sunwin_G1S_306': 0,
            'techplay_bc_77784': 0,
            'vingame_bc_77784': 0,
            'vingame_sb_77783': 0,
            'sb_77783': 0,
            'techplay_bacca_77778': 0,
            'vingame_bacca_77778': 0,
            'bacca_77778': 0,
            'xd_77786': 0,
            'vingame_xd_77786': 0
        })
        let rikViewersInterval: NodeJS.Timeout | null = null

        const getRandomNumber = (min: number, max: number) => {
            return Math.floor(Math.random() * (max - min + 1)) + min
        }

        const getNewViewersCount = (previousCount: number) => {
            if (previousCount === 0) {
                return getRandomNumber(200, 3000)
            }
            
            const fluctuation = getRandomNumber(-5, 5) / 100
            let newCount = Math.round(previousCount * (1 + fluctuation))
            
            return Math.max(200, newCount)
        }
        const listenNetworkChange = () => {
            async function checkConnection() {
              try {
                const response = await fetch('/assets/favicon/favicon.ico' + '?_=' + Date.now(), { method: 'HEAD', cache: 'no-store' });
                if (response.ok) {
                  isLostedInternet.value = false
                } else {
                  isLostedInternet.value = true
                }
              } catch (e) {
                isLostedInternet.value = true
              }
            }
          
            if (isSafari) {
                setInterval(checkConnection, 15000);
                checkConnection();
            } 
        }

        const initRikViewers = () => {
            if (rikViewersInterval) {
                clearInterval(rikViewersInterval)
            }
            
            previousRikViewers.value = {
                'rik_vgmn_108': 0,
                'rik_vgmn_109': 0,
                'rik_vgmn_110': 0,
                'rik_vgmn_111': 0,
                '789club_G1X_305': 0,
                '789club_G1X_306': 0,
                'sunwin_G1S_305': 0,
                'sunwin_G1X_306': 0,
                'sunwin_G1S_311': 0,
                'sunwin_G1S_306': 0,
                'techplay_bc_77784': 0,
                'vingame_bc_77784': 0,
                'vingame_sb_77783': 0,
                'sb_77783': 0,
                'bc_77784': 0,
                'xd_77786': 0,
                'vingame_xd_77786': 0
            }
            
            NO_DATA_VIEWER_LIVESTREAM_GAMES.forEach(gameId => {
                const initialViewers = getNewViewersCount(0)
                previousRikViewers.value[gameId] = initialViewers
                
                const existingIndex = viewers.value.findIndex(item => item.gameId === gameId)
                
                if (existingIndex !== -1) {
                    viewers.value[existingIndex].viewers = initialViewers
                } else {
                    viewers.value.push({
                        gameId,
                        viewers: initialViewers
                    })
                }
            })
            
            rikViewersInterval = setInterval(() => {
                NO_DATA_VIEWER_LIVESTREAM_GAMES.forEach(gameId => {
                    const newViewers = getNewViewersCount(previousRikViewers.value[gameId])
                    previousRikViewers.value[gameId] = newViewers
                    
                    const existingIndex = viewers.value.findIndex(item => item.gameId === gameId)
                    if (existingIndex !== -1) {
                        viewers.value[existingIndex].viewers = newViewers
                    }
                })
                
            }, 5000)
        }

        const fetchJackpot = async () => {
            try {
                const { data } = await fetcher.get('/slot/jackpot', { jackpot_cf: 1 })

                if (data?.value?.data) {
                    jackpots.value = data?.value?.data
                    totalJackpot.value = !!Object.values(jackpots.value)?.length
                        ? (Object.values(jackpots.value).reduce(
                              (acc, item) => acc + (!isNaN(+item) ? +item : 0),
                              0
                          ) as number)
                        : 0

                    previousJackpots.value = jackpots.value
                    previousTotalJackpot.value = totalJackpot.value
                    jackpotGo88.value = jackpotConditions
                        .filter((jackpot) => {
                            const key = jackpot.gameId
                            return (
                                key in jackpots.value &&
                                !!jackpots.value[key] &&
                                Number(jackpots.value[key]) > 0
                            )
                        })
                        .map((jackpot) => {
                            const key = jackpot.gameId
                            return {
                                ...jackpot,
                                jackpot: Number(jackpots.value[key]),
                            }
                        })
                        .sort((a, b) => +b.jackpot - +a.jackpot)
                    jackpotGo88Popup.value = jackpotGo88.value
                    prevJackpotGo88.value = jackpotGo88.value
                }
            } catch (error) {
                console.error('fetchJackpot error:', error)
            }
        }

        onServerPrefetch(async () => {
            try {
                await fetchJackpot()
            } catch (error) {
                console.error('Server-side fetchJackpot error:', error)
            }
        })
        // Initialize the socket connection on component mount
        onMounted(async() => {
            await nextTick(async () => {
                if (!totalJackpot.value || totalJackpot.value <= 0) {
                    await fetchJackpot()
                }
            })
            listenNetworkChange()
            socket.value = io(socketUrl, {
                reconnectionAttempts: 10, // number of attempts before close connection
                path: '/api/v1/ws',
                transports: ['websocket'], // use WebSocket
                withCredentials: true,
                query: { token },
            })

            // Listen for the "connect" event
            // socket.value.on('connect', () => {
            //     console.log(
            //         'Connected to Socket.IO server with ID:',
            //         socket.value.id
            //     )
            // })

            // Listen for incoming chat notificationList
            socket.value.on(
                'notification',
                (payload: ResponseParamsNotification[]) => {
                    notificationList.value = payload
                }
            )
            // Listen for incoming chat jackpotbroadcast
            socket.value.on(
                'jackpotbroadcast',
                (payload: { [key: string]: string }) => {
                    jackpots.value = payload
                    const key = 'go_vgmn_221'
                    jackpotGo88Aviator.value = jackpotConditions.filter(
                        item => item.gameId === key && Number(payload[key]) >= item.amount
                    ).map((jackpot) => {
                        return {
                            ...jackpot,
                            jackpot: Number(payload[key]),
                        }
                    })

                    jackpotGo88AviatorPopup.value = jackpotConditions.filter(
                        item => item.gameId === key && Number(payload[key]) > 0
                    ).map((jackpot) => {
                        return {
                            ...jackpot,
                            jackpot: Number(payload[key]),
                        }
                    })

                    totalJackpot.value = !!Object.values(payload)?.length
                        ? (Object.values(payload).reduce(
                              (acc, item) => acc + +item,
                              0
                          ) as number)
                        : 0

                    setTimeout(() => {
                        previousJackpots.value = jackpots.value
                        previousTotalJackpot.value = totalJackpot.value
                    }, 3000)
                }
            )

            socket.value.on(
                'go-bigwin',
                (payload: any) => {
                    bigWinUser.value = payload
                }
            )
            socket.value.on('go-jp-cf', (payload: any) => {
                const jsonPayload = JSON.parse(payload)
                jackpotGo88.value = jackpotConditions
                    .filter((jackpot) => {
                        const key = jackpot.gId
                        return (
                            jsonPayload[key] &&
                            jsonPayload[key].on === true &&
                            !isNaN(jsonPayload[key].currentValue) &&
                            Number(jsonPayload[key].currentValue) > 0
                        )
                    })
                    .map((jackpot) => {
                        const key = jackpot.gId
                        return {
                            ...jackpot,
                            jackpot: Number(jsonPayload[key]?.currentValue),
                        }
                    })
                    .sort((a, b) => +b.jackpot - +a.jackpot)
                if (Object.keys(jsonPayload).length === 0) {
                    return
                }
                jackpotGo88Popup.value = jackpotConditions
                    .filter((jackpot) => {
                        const key = jackpot.gId
                        return (
                            jsonPayload[key] &&
                            !isNaN(jsonPayload[key].currentValue) &&
                            Number(jsonPayload[key].currentValue) > 0
                        )
                    })
                    .map((jackpot) => {
                        const key = jackpot.gId
                        return {
                            ...jackpot,
                            jackpot: Number(jsonPayload[key]?.currentValue),
                        }
                    })
                    .sort((a, b) => +b.jackpot - +a.jackpot)
                jackpotGo88.value.push(...jackpotGo88Aviator.value)
                jackpotGo88Popup.value.push(...jackpotGo88AviatorPopup.value)

                setTimeout(() => {
                    prevJackpotGo88.value = jackpotGo88.value
                }, 3000)
            })

            socket.value.on('go-gamels', (payload: any) => {
                const data = JSON.parse(payload)
                let gameId = `go_${data.gameId}`
                
                if (gameId === 'go_vgmn_110') {
                    gameId = 'go_qs_txgo-101'
                }
                
                const existingIndex = viewers.value.findIndex(item => `${item.gameId}` === gameId)
                if (existingIndex !== -1) {
                    viewers.value[existingIndex].viewers = data.activeUsers
                } else {
                    viewers.value.push({gameId, viewers: data.activeUsers})
                }
            })
            socket.value.on('b52-gamels', (payload: any) => {
                const data = JSON.parse(payload)
                const existingIndex = viewers.value.findIndex(item => `${item.gameId}` === `b52_${data.gameId}`)
                if (existingIndex !== -1) {
                    viewers.value[existingIndex].viewers = data.activeUsers
                } else {
                    viewers.value.push({gameId: `b52_${data.gameId}`, viewers: data.activeUsers})
                }
                setTimeout(() => {
                    previousViewers.value = viewers.value
                }, 3000)
            })

            socket.value.on('techplay-gamels', (payload: any) => {
                const data = JSON.parse(payload)
                const existingIndex = viewers.value.findIndex(item => `${item.gameId}` === `techplay_${data.gameId}`)
                if (existingIndex !== -1) {
                    viewers.value[existingIndex].viewers = data.activeUsers
                } else {
                    viewers.value.push({gameId: `techplay_${data.gameId}`, viewers: data.activeUsers})
                }
                setTimeout(() => {
                    previousViewers.value = viewers.value
                }, 3000)
            })

            // Handle disconnect
            socket.value.on('disconnect', (reason: string) => {
                if (reason.includes('transport')) {
                    isLostedInternet.value = true
                }
            })
            socket.value.on('connect', () => {
                isLostedInternet.value = false
            })

            initRikViewers()
        })

        const isAllAsRead = computed(
            () =>
                !notificationList.value?.length ||
                (notificationList.value?.length &&
                    !notificationList.value.filter((e) => !e.is_readed).length)
        )

        // Disconnect socket on component unmount to avoid memory leaks
        onUnmounted(() => {
            if (socket.value) {
                socket.value.disconnect()
            }
            if (rikViewersInterval) {
                clearInterval(rikViewersInterval)
                rikViewersInterval = null
            }
        })

        return {
            notificationList,
            isAllAsRead,
            jackpots,
            totalJackpot,
            bigWinUser,
            previousTotalJackpot,
            previousJackpots,
            prevJackpotGo88,
            jackpotGo88,
            jackpotGo88Popup,
            viewers,
            isLostedInternet,
            previousViewers,
            fetchJackpot,
        }
    },
    {
        persist: {
            paths: [
                'jackpots',
                'totalJackpot',
                'bigWinUser',
                'previousTotalJackpot',
                'previousJackpots',
                'prevJackpotGo88',
                'jackpotGo88',
                'jackpotGo88Popup',
            ],
            storage: persistedState.localStorage,
        },
    }
)
