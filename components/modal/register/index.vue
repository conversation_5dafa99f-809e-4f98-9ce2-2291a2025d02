<template>
    <CommonModal
        :show="showRegisterModal"
        @close="closeSelf"
        size="md"
        class="font-primary"
        classCustomWrapper="items-start lg:items-center"
        classCustom="bg-z-chroma-black py-9 px-4 lg:px-6 rounded-[10px] sm:!max-w-[437px]"
    >
        <template #header>
            <div class="modal-header">
                <h3 class="mb-6 text-2xl font-bold uppercase">
                    {{ $t('auth.register.title') }}
                </h3>
                <button class="close absolute right-3 top-3" @click="closeSelf">
                    <NuxtIcon
                        class="mb-0 text-[2rem] text-white hover:opacity-70"
                        name="close"
                    ></NuxtIcon>
                </button>
            </div>
        </template>
        <div class="modal-body text-left tracking-wider">
            <form
                @submit.prevent="handleSubmitForm"
                id="register"
                class="form form-custom space-y-6"
            >
                <!-- <NuxtTurnstile v-model="token" /> -->
                <CommonTextInput
                    v-model.trim="username"
                    :label="t('auth.lbl_username')"
                    :placeholder="t('auth.plhd_username')"
                    :error="errors.username"
                    :errorMessage="
                        usernameExist ? t('error.username_exist') : null
                    "
                    :maxlength="MAX_LENGTH.USERNAME - 1"
                    inputClass="h-11"
                    class="w-full"
                    @focus="onFieldFocus"
                />
                <CommonTextInputPassword
                    v-model.trim="password"
                    :label="t('auth.lbl_password')"
                    :placeholder="t('auth.plhd_password')"
                    :error="errors.password"
                    :maxlength="MAX_LENGTH.PASSWORD"
                    inputClass="h-11 relative"
                    class="w-full"
                    isValidateSpace
                    isShowSuffixIcon
                    @focus="onFieldFocus"
                />
                <div>
                    <CommonTextInputNumber
                        v-model="phone"
                        type="tel"
                        :label="t('auth.lbl_phone')"
                        :placeholder="t('auth.plhd_phone')"
                        :error="errors.phone"
                        :maxlength="MAX_LENGTH.PHONE"
                        inputClass="h-11"
                        class="w-full"
                        @focus="onFieldFocus"
                    />
                    <p class="mb-0 mt-2 text-xs leading-relaxed text-[#a5adb6]">
                        {{ $t('auth.register.register_phone_note') }}
                    </p>
                </div>
                <input type="hidden" v-model="recaptchaToken" />
                <div
                    v-show="isV2Visible"
                    :id="v2ContainerId || 'recaptcha-v2-container'"
                    class="recaptcha-v2-container mt-4"
                ></div>
                <div>
                    <button
                        type="submit"
                        class="w-full rounded-md bg-z-red-dit p-3 font-bold uppercase leading-snug text-black hover:bg-z-solar-orange"
                        :loading="isSubmitting"
                        :disabled="isSubmitting"
                    >
                        {{ t('auth.register.button_register') }}
                    </button>
                    <div class="mt-4 text-center text-xs">
                        <a
                            target="_blank"
                            class="text-[#a5adb6]"
                            :href="PAGE_URL.TERM_SERVICES"
                        >
                            {{ t('auth.term_services_text') }}
                        </a>
                    </div>
                    <div class="form-custom-link mt-3 flex justify-center">
                        <button
                            type="button"
                            class="mx-auto inline-flex items-center gap-1 text-center text-sm font-bold uppercase text-white hover:text-z-red-dit"
                            @click="goToLogin"
                        >
                            <span
                                >{{ $t('auth.register.register_at') }}
                                {{ brandName }}</span
                            >
                            <NuxtIcon
                                class="mb-0 text-2xl text-z-red-dit"
                                name="arrow-right"
                            ></NuxtIcon>
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </CommonModal>
</template>
<script setup>
import { storeToRefs } from 'pinia'
import { useModalStore } from '~/stores'
import { useForm } from 'vee-validate'
import { useThrottleFn } from '@vueuse/core'

import { authForm } from '@/forms/auth.shema'
import { OK } from '~/constants/api-status'
import { PAGE_URL } from '~/constants/page-urls'
import { useUserStore } from '~/composables/use-user'
import { useRecaptcha } from '~/composables/use-recaptcha'
import { MAX_LENGTH, MIN_LENGTH, VALIDATE_RULE } from '~/constants'
import { useWindowSize } from '~/composables/use-window'

const { t } = useI18n()
const router = useRouter()
const useUserStoreInstance = useUserStore()
const useModalStoreInstance = useModalStore()
const {
    executeRecaptcha,
    isLoading: isRecaptchaLoading,
    isV2Visible,
    v2ContainerId,
    isV2Error,
    getV2Token,
    currentVersion,
    recaptchaToken,
} = useRecaptcha()
const { showRegisterModal, showRegisterSuccessModal } = storeToRefs(
    useModalStoreInstance
)
const { alert } = useAlert()
const initialValues = {
    username: '',
    password: '',
    phone: '',
}

const isLoading = ref(false)

const validationSchema = authForm(t)
const { $toast, $tracking } = useNuxtApp()
const { BRAND_NAME: brandName } = useRuntimeConfig().public
const { register, verifyUsername } = useUserStoreInstance
const { showLoginModal } = storeToRefs(useModalStoreInstance)
const { handleSubmit, errors, defineField, resetForm } = useForm({
    initialValues,
    validationSchema,
})
const { innerWidth } = useWindowSize()

const [username] = defineField('username')
const [password] = defineField('password')
const [phone] = defineField('phone')
const usernameExist = ref(false)

const isSubmitting = computed(() => isLoading.value || isRecaptchaLoading.value)
const recaptchaTriggered = ref(false)

const onFieldFocus = async () => {
    if (recaptchaTriggered.value) return
    recaptchaTriggered.value = true
    try {
        await executeRecaptcha('register')
    } catch (error) {
        console.warn(
            'reCAPTCHA error, continuing without token:',
            recaptchaError
        )
        isV2Error.value = true
    }
}

const handleRegisterSuccess = async () => {
    showRegisterModal.value = false
    showRegisterSuccessModal.value = true

    setTimeout(() => {
        showRegisterSuccessModal.value = false

        if (innerWidth.value <= 992) {
            router.push('/user/deposit-index')
        } else {
            router.push(PAGE_URL.DEPOSIT_LINK.CODEPAY)
        }
    }, 1400)
}

const handleRegisterError = (message) => {
    alert(message)
    showRegisterModal.value = true
}

const handleSubmitForm = handleSubmit(async (payload) => {
    $tracking('formSubmitted', { formName: 'Form_Register' })

    try {
        if (isSubmitting.value) return

        isLoading.value = true

        let payloadData = {
            ...payload,
        }

        if (!recaptchaToken.value) {
            payloadData = {
                ...payload,
                version: '',
            }
        } else {
            payloadData = {
                ...payload,
                token: recaptchaToken.value,
                version: currentVersion.value || '',
            }
        }

        const { data, error } = await register(payloadData)

        if (error.value) {
            throw error.value
        }

        if (data?.value?.status === OK) {
            await handleRegisterSuccess()
            return
        }

        if (data?.value?.status !== OK) {
            if (data.value.fallback && data.value.code === 202) {
                isV2Visible.value = true
                await getV2Token()
            } else {
                const errorMessage =
                    data?.value?.message || t('auth.register.error')
                handleRegisterError(errorMessage)
            }
            return
        }
    } catch (error) {
        const data = error.value?.data || null
        if (data?.fallback && data?.code === 202) {
            isV2Visible.value = true
            await getV2Token()
            return
        }

        let errorMessage = t('auth.register.error')

        if (error.message?.includes('reCAPTCHA')) {
            errorMessage =
                t('error.recaptcha') || 'reCAPTCHA verification failed'
        } else if (error.data?.message) {
            errorMessage = error.data.message
        } else if (error.message) {
            errorMessage = error.message
        }

        handleRegisterError(errorMessage)
    } finally {
        isLoading.value = false
    }
})

const handleVerifyUsername = useThrottleFn(async () => {
    if (!VALIDATE_RULE.REGISTER_USERNAME.test(username.value)) return

    if (
        username.value.length < MIN_LENGTH.USERNAME ||
        username.value.length > MAX_LENGTH.USERNAME
    )
        return

    const { data, error } = await verifyUsername({ username: username.value })

    if (error.value) {
        $toast(error?.value?.data?.message, 'error')
    }
    usernameExist.value = data.value.exist
}, 100)

const goToLogin = () => {
    showRegisterModal.value = false
    recaptchaToken.value = ''
    resetForm()
    usernameExist.value = false
    showLoginModal.value = true
}

const closeSelf = () => {
    showRegisterModal.value = false
    recaptchaToken.value = ''
    resetForm()
    usernameExist.value = false
}

watch(username, (newUsername) => {
    handleVerifyUsername(newUsername)
})
</script>
<style lang="scss" scoped>
:deep(.error-text) {
    @apply mt-[5px] font-normal leading-6 tracking-[1px] text-z-red-dit;
}
.recaptcha-v2-container {
    @apply hidden items-center justify-center;
}
</style>
