<template>
    <div class="box-information">
        <div class="box-information__progress flex items-center">
            <div :class="['progress w-full', isFull ? 'h-2' : 'h-1']">
                <div
                    :class="[
                        'progress-bar',
                        !svipInfo?.progress ? 'no-progress' : '',
                    ]"
                    role="progressbar"
                    :style="{
                        width: `${
                            svipInfo?.progress ? svipInfo?.progress : 0
                        }%`,
                    }"
                    :aria-valuenow="svipInfo?.progress ? svipInfo?.progress : 0"
                    aria-valuemin="0"
                    aria-valuemax="100"
                ></div>
            </div>
            <img
                v-if="!!svipInfo?.nextLevel"
                :class="[
                    'ml-3 object-contain',
                    isFull || isMobileLayout
                        ? 'h-6 w-6'
                        : 'h-[1.75rem] w-[1.75rem]',
                ]"
                :src="`${staticUrl}/svip/avatar/level_${svipInfo.nextLevel}.png`"
                alt="next avatar"
            />
        </div>
        <div v-if="!isFull" class="box-information__content mt-[2px]">
            <!-- <span class="level-vip" v-if="svipInfo?.total_deposit_this_month">{{
                NumberUtils.formatMoney(
                    svipInfo?.total_deposit_this_month || 0 / 1000,
                    'D'
                )
            }}</span> -->
            <span class="uppercase text-[#FBFDFF]">
                {{
                    svipInfo?.vip
                        ? `${$t(svipInfo.rankingVip)} ${svipInfo.ranking}`
                        : $t('user.overview.not_rank')
                }}
            </span>
            <span class="dis-level text-[#FBFDFF]">{{
                svipInfo?.deposit_vip_level || 0
            }}</span>
        </div>

        <div
            v-if="isFull || isMobileLayout"
            class="flex items-center justify-between gap-x-2"
        >
            <div
                class="text-[0.6875rem] font-semibold uppercase text-white lg:text-xs"
            >
                {{
                    svipInfo?.vip
                        ? `${$t(svipInfo.rankingVip)} ${svipInfo.ranking}`
                        : $t('user.overview.not_rank')
                }}
            </div>
            <div
                class="text-[0.6875rem] font-semibold text-z-dark-gray lg:text-xs"
            >
                {{ svipInfo?.deposit_vip_level || 0 }}
            </div>
        </div>

        <div
            v-if="svipInfo?.currentVipLevel < 15"
            :class="[
                'box-information__suggestion',
                isFull || isMobileLayout ? 'full-layout' : '',
            ]"
            v-html="
                t('user.overview.condition_to_next_rank', {
                    money: `<span class='text-[#FFA01B] font-medium'>
                            ${NumberUtils.formatMoney(
                                svipInfo.dis_deposit,
                                'D'
                            )}</span>`,
                    rank: `<span class='text-[#FFA01B] font-medium uppercase whitespace-nowrap'>
                            ${svipInfo.nextRanking}</span>`,
                })
            "
        />
    </div>
</template>

<script setup lang="ts">
const { staticUrl } = useRuntimeConfig().public
const { t } = useI18n()

const props = defineProps(['isFull', 'isMobileLayout'])

const useUserStoreInstance = useUserStore()
const { user, svipInfo } = storeToRefs(useUserStoreInstance)
</script>

<style scoped lang="scss">
.box-information {
    &__progress {
        .progress {
            @apply mx-auto my-0 rounded-xl bg-[#676767] after:content-none;

            .progress-bar {
                @apply relative h-full rounded-xl border-0 bg-[linear-gradient(90deg,_#F04734_0%,_#F49726_100%)];
                &::after {
                    @apply absolute -right-[0.5rem] top-2/4 h-3 w-3 -translate-y-2/4 rounded-[50%] bg-[url('/assets/images/svip/account/node.svg')] bg-contain bg-center bg-no-repeat content-[''];
                }
                &.no-progress {
                    &::after {
                        @apply content-none;
                    }
                }
            }
        }
    }

    &__content {
        @apply hidden items-center justify-between font-montserrat text-[0.625rem] font-normal leading-[1.4] lg:flex;

        .level-vip {
            @apply text-z-pale-grey;
        }
        .dis-level {
            @apply leading-[normal] text-z-pale-grey;
        }
    }

    &__suggestion {
        @apply mt-2 text-[0.625rem] font-normal leading-[1.4] text-z-pale-grey;
        &.full-layout {
            @apply mt-3 text-sm text-white;
            .level-vip,
            .dis-level {
                @apply text-white;
            }
        }
    }
}
</style>
