<script lang="ts" setup>
import { ref, watch, onMounted, onUnmounted } from 'vue'
import { NumberUtils } from '~/utils'

const props = defineProps({
    number: {
        type: Number,
        default: 0,
    },
    suffix: {
        type: String,
        default: '',
    },
    prefix: {
        type: String,
        default: '',
    },
    previousNumber: {
        type: Number,
        default: 0,
    },
    className: {
        type: String,
        default: '',
    },
    isHeroBanner: {
        type: Boolean,
        default: false,
    },
})

const numberCounter = ref<string>(
    props.isHeroBanner
        ? NumberUtils.formatNumberWithComma(props.previousNumber)
        : '0'
)

numberCounter.value = NumberUtils.formatNumberWithComma(props.number)

const getAnimationDuration = (num: number): number => {
    if (num < 1000000) {
        return 300
    }
    return 1000
}

let animationDuration = getAnimationDuration(props.number)
let animationStartTime: number | null = null
let animationFrameId: number | null = null
let previousNumber = props.previousNumber
let lastUpdateTime = 0

const animateNumber = (timestamp: number): void => {
    const animationDelay = 70
    if (timestamp - lastUpdateTime >= animationDelay) {
        lastUpdateTime = timestamp
        if (animationStartTime === null) {
            animationStartTime = timestamp
        }

        const progress = Math.min(
            1,
            (timestamp - animationStartTime) / animationDuration
        )
        const newCounterValue = Math.round(
            previousNumber + (props.number - previousNumber) * progress
        )

        numberCounter.value = NumberUtils.formatNumberWithComma(newCounterValue)

        if (progress < 1) {
            animationFrameId = requestAnimationFrame(animateNumber)
        } else {
            numberCounter.value = NumberUtils.formatNumberWithComma(
                Math.round(props.number)
            )
        }
    } else {
        animationFrameId = requestAnimationFrame(animateNumber)
    }
}

const opacityClass = computed(() => {
    return props.number === 0 ? 'opacity-0' : 'opacity-100'
})

onMounted(() => {
    previousNumber = props.previousNumber
    animationFrameId = requestAnimationFrame(animateNumber)
})

onUnmounted(() => {
    if (animationFrameId !== null) {
        cancelAnimationFrame(animationFrameId)
    }
})

watch(
    () => props.number,
    () => {
        previousNumber = parseFloat(numberCounter.value.replace(/,/g, ''))
        animationStartTime = null
        animationDuration = getAnimationDuration(props.number)
        if (animationFrameId !== null) {
            cancelAnimationFrame(animationFrameId)
        }
        animationFrameId = requestAnimationFrame(animateNumber)
    }
)
</script>

<template>
    <span
        class="font-roboto text-xs text-z-pale-grey xl:text-sm opacity-0"
        :class="[className, opacityClass]"
    >
        <span v-if="prefix" class="mr-1 xl:mr-1.5">{{ prefix }}</span>
        {{ numberCounter }}
        <strong class="ml-1 xl:ml-1.5" v-if="suffix">{{ suffix }}</strong>
    </span>
</template>
