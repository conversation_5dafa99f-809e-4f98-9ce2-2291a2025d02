<script setup lang="ts">
import { IMAGE_LINK_DEFAULT } from '~/constants/common'
import VLazyImage from 'v-lazy-image'

const props = defineProps({
    src: {
        type: String,
        require: true,
        default: '',
    },
    srcMb: {
        type: String,
        require: true,
        default: '',
    },
    imageDefault: {
        type: String,
        default: '',
    },
    alt: {
        type: String,
        default: '',
    },
    lazyload: {
        type: Boolean,
        default: false,
    },
    classWrapper: {
        type: String,
        default: '',
    },
    class: {
        type: String,
        default: '',
    },
    max: {
        type: String,
        default: '(max-width: 991px)',
    },
    width: {
        type: [String, Number],
        default: '',
    },
    height: {
        type: [String, Number],
        default: '',
    },
    sizes: {
        type: String,
        default: '100vw',
    },
    loading: {
        type: String,
        default: 'lazy',
    },
    fetchpriority: {
        type: String,
        default: 'low',
    },
    onerror: {
        type: String,
        default: '',
    },
    isDefaultImageClass: {
        type: Boole<PERSON>,
        default: false,
    },
})
const emit = defineEmits(['load', 'error'])

const imageSrc = ref<string>(props.src)
const errorClass = computed(() => imageSrc.value === IMAGE_LINK_DEFAULT ? ' error-image' : '')

const imageSrcDefault = computed(() => props.imageDefault || IMAGE_LINK_DEFAULT)
const defaultImageClass = ref('')
watchEffect(() => {
    imageSrc.value = props.src
})

const checkImageError = (): void => {
    imageSrc.value = imageSrcDefault.value
    emit('error')
}

const handleImageLoad = (): void => {
    emit('load')
}

const convertFileNameExtension = (filePath = '', extension = ''): string => {
    const pos = filePath.lastIndexOf('.')
    return `${filePath.substr(0, pos < 0 ? filePath.length : pos)}${extension}`
}

const webpSrc = computed(() => convertFileNameExtension(props.src, '.webp'))
const avifSrc = computed(() => convertFileNameExtension(props.src, '.avif'))

const webpSrcMobile = computed(() =>
    convertFileNameExtension(props.srcMb, '.webp')
)
const avifSrcMobile = computed(() =>
    convertFileNameExtension(props.srcMb, '.avif')
)

const className = computed(() => props.class + errorClass.value)
</script>
<template>
    <div class="base-image" :class="`${classWrapper} ${isDefaultImageClass ? 'flex items-center justify-center' : ''}`">
        <picture v-if="!lazyload" v-bind="props">
            <template v-if="srcMb">
                <source
                    :srcset="avifSrcMobile"
                    type="image/avif"
                    :media="max"
                />
                <source
                    :srcset="webpSrcMobile"
                    type="image/webp"
                    :media="max"
                />
            </template>
            <source :srcset="avifSrc" type="image/avif" />
            <source :srcset="webpSrc" type="image/webp" />
            <img
                :src="src"
                :alt="alt"
                :class="className"
                :width="width"
                :height="height"
                :loading="loading"
                :fetchpriority="fetchpriority"
                :sizes="sizes"
                :onerror="onerror"
                @load="handleImageLoad"
                @error="checkImageError"
            />
        </picture>
        <v-lazy-image
            v-else
            v-bind="$attrs"
            :src="imageSrc"
            @error="checkImageError"
            @load="handleImageLoad"
            :class="`${className} ${isDefaultImageClass ? '!w-auto !h-auto !min-w-0 !min-h-0' : ''}`"
            :src-placeholder="imageSrcDefault"
            :alt="alt"
            :width="width"
            :height="height"
        />
    </div>
</template>
