<template>
    <div
        @click="onClickHeroBanner"
        class="relative aspect-[375/100] lg:aspect-[1920/319]"
    >
        <div
            :class="[
                'banner absolute inset-0',
                { 'cursor-pointer': dataBanner.link || jackpots?.length },
            ]"
        >
            <CommonImage
                :src="dataBanner.src"
                :srcMb="dataBanner.srcMb"
                alt="banner"
                class="h-auto w-full"
            />
            <div
                v-if="jackpots?.length"
                class="jackpot xxl:bottom-[18%] absolute bottom-2 left-[7%] sm:bottom-[15%] sm:left-[5%] md:bottom-[10%] lg:left-[19%]"
            >
                <CommonImage
                    class="h-[2.375rem] w-[13.25rem] animate-blink md:h-[6vw] md:w-[34.35vw]"
                    :max="`(max-width: 991px)`"
                    :src="`${staticUrl}/banners/jackpot/title-pc.png`"
                    :srcMb="`${staticUrl}/banners/jackpot/title-mb.png`"
                    alt="title"
                    loading="eager"
                    :fetchpriority="'high'"
                />
                <div
                    class="mt-1.5 h-10 w-[9.625rem] md:h-[4.5rem] md:w-[21rem] lg:mt-3 lg:h-[5.5rem] lg:w-[24.625rem]"
                >
                    <Swiper
                        ref="swiperRef"
                        :modules="[
                            Autoplay,
                            EffectCards,
                            EffectCreative,
                            FreeMode,
                            EffectFade,
                        ]"
                        :loop="true"
                        :slides-per-view="1"
                        :spaceBetween="0"
                        :fadeEffect="{
                            crossFade: true,
                        }"
                        :autoplay="{
                            delay: 5000,
                            disableOnInteraction: false,
                            pauseOnMouseEnter: true,
                        }"
                        @swiper="onSwiper"
                        @slideChange="onSlideChange"
                    >
                        <SwiperSlide
                            v-for="(item, index) in jackpots"
                            :key="index"
                            @click="
                                openGame(
                                    item.game,
                                    item.name.includes('live'),
                                    false
                                )
                            "
                        >
                            <div
                                class="jackpot-item relative h-10 w-[9.625rem] cursor-pointer md:h-[4.5rem] md:w-[21rem] lg:h-[5.5rem] lg:w-[24.625rem]"
                            >
                                <img
                                    class="h-10 w-[9.625rem] md:h-[4.5rem] md:w-[21rem] lg:h-[5.5rem] lg:w-[24.625rem]"
                                    :src="`${staticUrl}/home/<USER>/jackpot/${item.name}.avif`"
                                    :alt="item.name"
                                />
                                <div
                                    class="absolute bottom-[5px] right-1 flex w-[9.625rem] items-center justify-end md:bottom-1 lg:right-3 lg:w-[24.625rem]"
                                >
                                    <CommonAnimatedNumber
                                        className="custom-number !font-open_sans font-extrabold lg:!text-[1.875rem] lg:tracking-[-1px] md:!text-[1.5rem] !text-[0.6875rem] !leading-[calc(28/11)] lg:leading-[calc(40/30)]"
                                        :number="item.jackpot"
                                        :previousNumber="
                                            prevJackpots[index]?.jackpot
                                                ? +prevJackpots[index]?.jackpot
                                                : 0
                                        "
                                        suffix="VND"
                                    />
                                </div>
                            </div>
                        </SwiperSlide>
                    </Swiper>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import {
    Autoplay,
    EffectCards,
    EffectCreative,
    FreeMode,
    EffectFade,
} from 'swiper/modules'
import type { IGameBanner, JackpotGame } from '~/interfaces/game'
import { usePlayGame } from '~/composables/use-play-game'
import type SwiperCore from 'swiper'

const props = defineProps({
    dataBanner: {
        type: Object as () => IGameBanner,
        default: '',
    },
    jackpots: {
        type: Array as () => JackpotGame[],
        default: [] as JackpotGame[],
    },
    prevJackpots: {
        type: Array as () => JackpotGame[],
        default: [] as JackpotGame[],
    },
})

const { staticUrl } = useRuntimeConfig().public
const swiperRef = ref<SwiperCore | null>(null)
const activeSlideIndex = ref(0)
const onSwiper = (swiperInstance) => {
    swiperRef.value = swiperInstance
}
const onSlideChange = (swiper) => {
    if (swiperRef.value) {
        activeSlideIndex.value = swiper.realIndex
    }
}

const { openGame } = usePlayGame()
const onClickHeroBanner = () => {
    if (props.jackpots?.length) {
        openGame(
            props.jackpots[activeSlideIndex.value]?.game,
            props.jackpots[activeSlideIndex.value]?.name.includes('live'),
            false
        )
    }
}
</script>
<style lang="scss" scoped>
.custom-number {
    @apply whitespace-nowrap text-xs md:text-[1.1rem] md:leading-[1.1] lg:text-[2rem] lg:leading-[1.2];
}
</style>
