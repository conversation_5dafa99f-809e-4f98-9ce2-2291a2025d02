const CACHE_NAME = 'z01sv-cache-v2'
const STATIC_CACHE_NAME = 'z01sv-static-v2'
const DYNAMIC_CACHE_NAME = 'z01sv-dynamic-v2'
const CRITICAL_CACHE_NAME = 'z01sv-critical-v2'

// Critical assets that must be cached immediately
const CRITICAL_ASSETS = [
    '/',
    '/assets/scss/style.scss',
    '/assets/favicon/favicon.ico',
    // Critical hero banner images only
    '/assets/images/home/<USER>/jackpot/pc.avif',
    '/assets/images/home/<USER>/jackpot/pc.webp',
    '/assets/images/home/<USER>/jackpot/pc.jpg',
    '/assets/images/home/<USER>/jackpot/mb.avif',
    '/assets/images/home/<USER>/jackpot/mb.webp',
    '/assets/images/home/<USER>/jackpot/mb.jpg',
]

// Static assets for normal caching
const STATIC_ASSETS = [
    '/assets/js/nanoplayer.4.min.js',
    // Other hero banners
    '/assets/images/home/<USER>/golden-star-warrios/pc.jpg',
    '/assets/images/home/<USER>/golden-star-warrios/mb.jpg',
    '/assets/images/home/<USER>/club-world-cup/pc.jpg',
    '/assets/images/home/<USER>/club-world-cup/pc.webp',
    '/assets/images/home/<USER>/club-world-cup/pc.avif',
    '/assets/images/home/<USER>/club-world-cup/mb.jpg',
    '/assets/images/home/<USER>/club-world-cup/mb.webp',
    '/assets/images/home/<USER>/club-world-cup/mb.avif',
    '/assets/images/home/<USER>/danh-de-mien-phi/pc.png',
    '/assets/images/home/<USER>/danh-de-mien-phi/pc.avif',
    '/assets/images/home/<USER>/danh-de-mien-phi/pc.webp',
    '/assets/images/home/<USER>/danh-de-mien-phi/mb.png',
    '/assets/images/home/<USER>/danh-de-mien-phi/mb.avif',
    '/assets/images/home/<USER>/danh-de-mien-phi/mb.webp',
]

const CACHEABLE_EXTENSIONS = [
    '.css',
    '.js',
    '.png',
    '.jpg',
    '.jpeg',
    '.gif',
    '.svg',
    '.webp',
    '.avif',
    '.woff',
    '.woff2',
    '.ttf',
    '.eot',
    '.ico',
]

// domain allowed to cache (fonts, analytics, CDN)
const ALLOWED_DOMAINS = [
    'fonts.googleapis.com', // Google Fonts CSS
    'fonts.gstatic.com', // Google Fonts files
    'www.googletagmanager.com', // Google Analytics
    'www.google-analytics.com', // Google Analytics
    'ssl.google-analytics.com', // Google Analytics SSL
    'cdnjs.cloudflare.com', // Cloudflare CDN
    'unpkg.com', // UNPKG CDN
    'jsdelivr.net', // jsDelivr CDN
    'cdn.jsdelivr.net', // jsDelivr CDN
]

// Install event - cache critical assets first, then static assets
self.addEventListener('install', (event) => {
    console.log('[SW] Installing service worker...')
    event.waitUntil(
        // Cache critical assets first
        caches
            .open(CRITICAL_CACHE_NAME)
            .then((criticalCache) => {
                console.log('[SW] Caching critical assets')
                return criticalCache.addAll(CRITICAL_ASSETS)
            })
            .then(() => {
                console.log('[SW] Critical assets cached successfully')
                // Then cache static assets
                return caches.open(STATIC_CACHE_NAME)
            })
            .then((staticCache) => {
                console.log('[SW] Caching static assets')
                return staticCache.addAll(STATIC_ASSETS)
            })
            .then(() => {
                console.log('[SW] Static assets cached successfully')
                return self.skipWaiting()
            })
            .catch((error) => {
                console.error('[SW] Failed to cache assets:', error)
            })
    )
})

// Activate event - cleanup old caches
self.addEventListener('activate', (event) => {
    console.log('[SW] Activating service worker...')
    event.waitUntil(
        caches
            .keys()
            .then((cacheNames) => {
                return Promise.all(
                    cacheNames.map((cacheName) => {
                        if (
                            cacheName !== STATIC_CACHE_NAME &&
                            cacheName !== DYNAMIC_CACHE_NAME
                        ) {
                            console.log('[SW] Deleting old cache:', cacheName)
                            return caches.delete(cacheName)
                        }
                    })
                )
            })
            .then(() => {
                console.log('[SW] Service worker activated')
                return self.clients.claim()
            })
    )
})

// Fetch event - intercept network requests
self.addEventListener('fetch', (event) => {
    const { request } = event
    const url = new URL(request.url)

    // Skip request that not need to cache
    if (
        request.method !== 'GET' ||
        request.headers.get('range') ||
        url.protocol === 'chrome-extension:' ||
        url.protocol === 'moz-extension:'
    ) {
        return
    }

    // Cache strategy for external allowed resources (fonts, analytics)
    if (isExternalAllowedResource(url)) {
        event.respondWith(cacheFirstStrategy(request))
    }
    // Cache strategy for CSS and JS
    else if (isCacheableResource(url)) {
        event.respondWith(cacheFirstStrategy(request))
    }
    // Cache strategy for images
    else if (isImageResource(url)) {
        event.respondWith(cacheFirstStrategy(request))
    }
    // Cache strategy for fonts
    else if (isFontResource(url)) {
        event.respondWith(cacheFirstStrategy(request))
    }
    // Network first for API calls
    else if (isApiRequest(url)) {
        event.respondWith(networkFirstStrategy(request))
    }
    // Stale while revalidate for HTML pages
    else {
        event.respondWith(staleWhileRevalidateStrategy(request))
    }
})

// Check if resource can be cached
function isCacheableResource(url) {
    return (
        CACHEABLE_EXTENSIONS.some((ext) => url.pathname.endsWith(ext)) ||
        url.pathname.includes('/_nuxt/') ||
        url.pathname.includes('/assets/')
    )
}

// Check if resource is image
function isImageResource(url) {
    const imageExtensions = [
        '.png',
        '.jpg',
        '.jpeg',
        '.gif',
        '.svg',
        '.webp',
        '.avif',
    ]
    return (
        imageExtensions.some((ext) => url.pathname.endsWith(ext)) ||
        url.pathname.includes('/assets/images/')
    )
}

// Check if resource is font
function isFontResource(url) {
    const fontExtensions = ['.woff', '.woff2', '.ttf', '.eot']
    return (
        fontExtensions.some((ext) => url.pathname.endsWith(ext)) ||
        ALLOWED_DOMAINS.some((domain) => url.hostname.includes(domain))
    )
}

// Check if it's an external resource from allowed domains
function isExternalAllowedResource(url) {
    return ALLOWED_DOMAINS.some((domain) => url.hostname.includes(domain))
}

// Check if resource is API request
function isApiRequest(url) {
    return (
        url.pathname.startsWith('/api/') ||
        url.pathname.startsWith('/api-promotion/')
    )
}

// Cache First Strategy - priority cache, fallback to network
async function cacheFirstStrategy(request) {
    try {
        const cachedResponse = await caches.match(request)
        if (cachedResponse) {
            return cachedResponse
        }

        const networkResponse = await fetch(request)
        if (networkResponse.ok) {
            const cache = await caches.open(DYNAMIC_CACHE_NAME)
            cache.put(request, networkResponse.clone())
        }
        return networkResponse
    } catch (error) {
        console.error('[SW] Cache first strategy failed:', error)
        return new Response('Network error', { status: 503 })
    }
}

// Network First Strategy - priority network, fallback to cache
async function networkFirstStrategy(request) {
    try {
        const networkResponse = await fetch(request)
        if (networkResponse.ok) {
            const cache = await caches.open(DYNAMIC_CACHE_NAME)
            cache.put(request, networkResponse.clone())
        }
        return networkResponse
    } catch (error) {
        console.error('[SW] Network first strategy failed:', error)
        const cachedResponse = await caches.match(request)
        if (cachedResponse) {
            return cachedResponse
        }
        return new Response('Network error', { status: 503 })
    }
}

// Stale While Revalidate Strategy - return cache immediately, update cache in background
async function staleWhileRevalidateStrategy(request) {
    const cache = await caches.open(DYNAMIC_CACHE_NAME)
    const cachedResponse = await cache.match(request)

    const fetchPromise = fetch(request)
        .then((networkResponse) => {
            if (networkResponse.ok) {
                cache.put(request, networkResponse.clone())
            }
            return networkResponse
        })
        .catch((error) => {
            console.error('[SW] Stale while revalidate failed:', error)
            return (
                cachedResponse || new Response('Network error', { status: 503 })
            )
        })

    return cachedResponse || fetchPromise
}

// Background sync cho offline functionality
self.addEventListener('sync', (event) => {
    if (event.tag === 'background-sync') {
        console.log('[SW] Background sync triggered')
        event.waitUntil(doBackgroundSync())
    }
})

async function doBackgroundSync() {
    try {
        // Perform background sync when network is available
        console.log('[SW] Performing background sync...')
    } catch (error) {
        console.error('[SW] Background sync failed:', error)
    }
}

// Push notification handling
self.addEventListener('push', (event) => {
    if (event.data) {
        const data = event.data.json()
        const options = {
            body: data.body || 'Bạn có thông báo mới!',
            icon: '/assets/favicon/favicon.ico',
            badge: '/assets/favicon/favicon.ico',
            vibrate: [100, 50, 100],
            data: {
                dateOfArrival: Date.now(),
                primaryKey: 1,
            },
            actions: [
                {
                    action: 'explore',
                    title: 'Xem chi tiết',
                    icon: '/assets/favicon/favicon.ico',
                },
                {
                    action: 'close',
                    title: 'Đóng',
                    icon: '/assets/favicon/favicon.ico',
                },
            ],
        }

        event.waitUntil(
            self.registration.showNotification(data.title || 'Z01', options)
        )
    }
})

// Notification click handling
self.addEventListener('notificationclick', (event) => {
    event.notification.close()

    if (event.action === 'explore') {
        event.waitUntil(clients.openWindow('/'))
    }
})

// Message handling from main thread
self.addEventListener('message', (event) => {
    if (event.data && event.data.type === 'SKIP_WAITING') {
        self.skipWaiting()
    }

    if (event.data && event.data.type === 'GET_VERSION') {
        event.ports[0].postMessage({ version: CACHE_NAME })
    }
})
