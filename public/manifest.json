{"name": "Z01SV - Cổng game trực tuyến", "short_name": "Z01SV", "description": "Cổng game trực tuyến hàng đầu Việt Nam với nhiều trò chơi hấp dẫn", "start_url": "/", "display": "standalone", "background_color": "#ffffff", "theme_color": "#007bff", "orientation": "portrait-primary", "scope": "/", "lang": "vi", "dir": "ltr", "categories": ["games", "entertainment"], "icons": [{"src": "/assets/favicon/favicon.ico", "sizes": "16x16", "type": "image/x-icon"}, {"src": "/assets/favicon/favicon.ico", "sizes": "32x32", "type": "image/x-icon"}, {"src": "/assets/favicon/favicon.ico", "sizes": "48x48", "type": "image/x-icon"}, {"src": "/assets/favicon/favicon.ico", "sizes": "64x64", "type": "image/x-icon"}, {"src": "/assets/favicon/favicon.ico", "sizes": "128x128", "type": "image/x-icon"}, {"src": "/assets/favicon/favicon.ico", "sizes": "256x256", "type": "image/x-icon"}, {"src": "/assets/favicon/favicon.ico", "sizes": "512x512", "type": "image/x-icon", "purpose": "any maskable"}], "screenshots": [{"src": "/assets/images/home/<USER>/golden-star-warrios/pc.jpg", "sizes": "1280x720", "type": "image/jpeg", "form_factor": "wide"}, {"src": "/assets/images/home/<USER>/golden-star-warrios/mb.jpg", "sizes": "750x1334", "type": "image/jpeg", "form_factor": "narrow"}], "shortcuts": [{"name": "Trang chủ", "short_name": "Home", "description": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> trang chủ", "url": "/", "icons": [{"src": "/assets/favicon/favicon.ico", "sizes": "96x96"}]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON> mãi", "short_name": "Promo", "description": "<PERSON><PERSON> c<PERSON> mãi", "url": "/khuyen-mai", "icons": [{"src": "/assets/favicon/favicon.ico", "sizes": "96x96"}]}, {"name": "<PERSON> tức", "short_name": "News", "description": "<PERSON><PERSON><PERSON> tin tức mới nhất", "url": "/tin-tuc", "icons": [{"src": "/assets/favicon/favicon.ico", "sizes": "96x96"}]}], "related_applications": [], "prefer_related_applications": false, "edge_side_panel": {"preferred_width": 400}, "launch_handler": {"client_mode": "navigate-existing"}, "protocol_handlers": [{"protocol": "web+game", "url": "/game?url=%s"}], "file_handlers": [{"action": "/upload", "accept": {"image/*": [".png", ".jpg", ".jpeg", ".gif", ".webp", ".avif"]}}], "share_target": {"action": "/share", "method": "POST", "enctype": "multipart/form-data", "params": {"title": "title", "text": "text", "url": "url", "files": [{"name": "files", "accept": ["image/*", "text/*"]}]}}}