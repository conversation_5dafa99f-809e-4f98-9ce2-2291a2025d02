<template>
    <div class="home-page bg-[#161312]">
        <HomeHeroBanner />
        <div class="container pt-4 lg:pt-12">
            <div
                :class="[
                    'flex flex-wrap items-center gap-4 lg:flex-nowrap',
                    hotMatch?.data?.length && !showMobile ? 'max-md:aspect-[400/214]' : 'mt-2',
                ]"
            >
                <HomePromotion
                    class="order-2 w-full lg:order-1 lg:min-h-[7.6875rem] lg:w-auto lg:flex-1"
                />
                <HomeHotMatch
                    v-if="hotMatch?.data?.length && !showMobile"
                    class="lg-w-auto order-1 w-full lg:order-2 max-lg:hidden"
                />
            </div>
            <HomeGameCard  v-if="!showMobile" class="max-lg:hidden"/>
            <HomeLivestream :class="[!showMobile ? '' : 'mt-6']"/>
            <HomeHotGame />
            <HomeSlotGame />
            <HomeTopWin />
        </div>
        <HomeIntroduction />
    </div>
</template>
<script setup lang="ts">
import { useHotMatchStore } from '~/composables/home/<USER>'
import { storeToRefs } from 'pinia'

const useHotMatchInstance = useHotMatchStore()
const { showMobile } = useCommon()
const { hotMatch } = storeToRefs(useHotMatchInstance)
const socketStoreInstance = useSocket()
const { fetchJackpot } = socketStoreInstance
fetchJackpot()
</script>
<style>
@media screen and (max-width: 1025px) {
    .fix-ratio {
        aspect-ratio: 4.855 / 1;
    }
}

@media screen and (max-width: 768px) {
    .fix-ratio {
        aspect-ratio: 2.368 / 1;
        width: 100%;
        height: auto;
    }
}
</style>
