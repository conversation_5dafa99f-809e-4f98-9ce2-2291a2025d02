# Performance Optimization Guide

## Render Blocking CSS Optimization

Dự án đã được tối ưu hóa để giải quyết vấn đề **Render Blocking Requests** với ước tính tiết kiệm **1,530ms**.

### 🎯 Vấn đề đã giải quyết

Các file CSS sau đây đã được tối ưu hóa để không còn block việc render trang:

- `/_nuxt/index.C42_iUI5.css` (41.9 KiB, 1,830ms)
- `/_nuxt/button.B9v6vxHo.css` (1.0 KiB, 480ms)  
- `/_nuxt/entry.ceyXRjo-.css` (48.3 KiB, 2,130ms)
- `/_nuxt/index.D2C6dFch.css` (0.9 KiB, 180ms)
- `/_nuxt/index.Dy9eSoo3.css` (0.7 KiB, 180ms)
- `/_nuxt/index.AJ5clg8T.css` (2.4 KiB, 180ms)
- `/_nuxt/default.D7SrQHRa.css` (0.9 KiB, 180ms)
- `/_nuxt/login-form.CYniJ5Ez.css` (0.7 KiB, 180ms)
- `/_nuxt/nuxt-icon.D08378P0.css` (0.5 KiB, 480ms)
- `/_nuxt/swiper-vue.D2zhFq2K.css`

### 🚀 Các Plugin Tối Ưu Hóa

#### 1. Dynamic CSS Optimizer Plugin (`plugins/dynamic-css-optimizer.client.ts`) ⭐ NEW

**Giải quyết vấn đề CSS files có hash động:**
- ✅ **Pattern-based detection** - Tự động nhận diện critical/non-critical CSS
- ✅ **Dynamic categorization** - Phân loại CSS files dựa trên tên file patterns
- ✅ **Hash-agnostic optimization** - Hoạt động với bất kỳ hash nào
- ✅ **Inline critical CSS** cho above-the-fold content
- ✅ **Progressive enhancement** cho below-the-fold content

**Cách hoạt động với hash động:**
```typescript
// Tự động phát hiện critical CSS bất kể hash
const isCritical = href.includes('/entry.') ||    // entry.ABC123.css
                  href.includes('/index.') ||     // index.XYZ789.css
                  href.includes('/app.') ||       // app.DEF456.css
                  href.includes('/main.')         // main.GHI012.css

// Tự động phát hiện non-critical CSS
const isNonCritical = href.includes('swiper') ||  // swiper-vue.ABC123.css
                     href.includes('icon') ||     // nuxt-icon.XYZ789.css
                     href.includes('button')      // button.DEF456.css

// Defer non-critical CSS
element.setAttribute('rel', 'preload')
element.setAttribute('as', 'style')
element.setAttribute('onload', "this.onload=null;this.rel='stylesheet'")
```

#### 2. CSS Optimization Plugin (`plugins/css-optimization.client.ts`)

**Chức năng bổ sung:**
- ✅ Remove unused CSS (basic implementation)
- ✅ Optimize font loading với `font-display: swap`
- ✅ Advanced progressive enhancement

#### 2. JavaScript Optimization Plugin (`plugins/js-optimization.client.ts`)

**Chức năng:**
- ✅ Defer non-critical JavaScript
- ✅ Lazy load third-party scripts
- ✅ Optimize inline scripts
- ✅ Preload critical JavaScript modules
- ✅ Code splitting optimization

**Cách hoạt động:**
```typescript
// Lazy load third-party scripts
const thirdPartyScripts = [
    {
        src: 'https://www.googletagmanager.com/gtag/js',
        condition: () => window.scrollY > 100
    }
]

// Load on user interaction
interactionEvents.forEach(event => {
    document.addEventListener(event, loadScripts, { once: true })
})
```

#### 3. Resource Hints Plugin (`plugins/resource-hints.client.ts`)

**Chức năng:**
- ✅ Dynamic preload cho critical resources
- ✅ Intelligent DNS prefetch
- ✅ Preconnect optimization với timing
- ✅ Module preload cho critical JavaScript
- ✅ Adaptive preloading dựa trên connection quality
- ✅ Intersection Observer cho lazy preloading

**Cách hoạt động:**
```typescript
// Adaptive preloading based on connection
const connection = navigator.connection
if (connection.effectiveType === 'slow-2g') {
    // Only preload critical resources
    preloadCriticalOnly()
} else {
    // Full preloading
    preloadAll()
}
```

#### 4. Service Worker Optimization (`public/sw.js`) ⭐ UPDATED

**Chức năng nâng cao cho hash động:**
- ✅ **Dynamic pattern detection** cho CSS files
- ✅ **Hash-agnostic caching strategy**
- ✅ **Critical CSS priority caching**
- ✅ **Fallback CSS** khi network fail

**Cách hoạt động với hash động:**
```javascript
// Dynamic CSS detection
const isCriticalCSS = pathname.includes('/entry.') ||    // entry.ABC123.css
                     pathname.includes('/index.') ||     // index.XYZ789.css
                     pathname.includes('/app.')          // app.DEF456.css

const isNonCriticalCSS = pathname.includes('swiper') ||  // swiper.ABC123.css
                        pathname.includes('icon')        // icon.XYZ789.css

// CSS Optimized Strategy với fallback
if (isCriticalCSS) {
    // Cache first với fallback CSS
    return criticalCSSWithFallback(request)
} else if (isNonCriticalCSS) {
    // Stale while revalidate
    return staleWhileRevalidateStrategy(request)
}
```

### ⚙️ Cấu Hình Nuxt.config.ts

**Các thay đổi quan trọng:**

```typescript
experimental: {
    inlineSSRStyles: true, // Inline critical CSS
    treeshakeClientOnly: true, // Remove unused code
},

vitalizer: {
    disableStylesheets: 'entry', // Disable render blocking stylesheets
    disablePrefetchLinks: true,
},

build: {
    extractCSS: {
        chunkFilename: '_nuxt/[name].[contenthash:8].css' // Split CSS chunks
    },
    rollupOptions: {
        output: {
            assetFileNames: (assetInfo) => {
                if (assetInfo.name?.endsWith('.css')) {
                    return '_nuxt/css/[name].[hash][extname]'
                }
                return '_nuxt/[name].[hash][extname]'
            }
        }
    }
}
```

### 📊 Kết Quả Dự Kiến

**Trước khi tối ưu hóa:**
- Render blocking requests: 1,530ms
- LCP (Largest Contentful Paint): >4s
- FCP (First Contentful Paint): Chậm

**Sau khi tối ưu hóa:**
- ✅ Giảm render blocking time: ~1,530ms
- ✅ Cải thiện LCP: <2.5s (mục tiêu)
- ✅ Cải thiện FCP: <1.8s
- ✅ Tăng Performance Score: +20-30 điểm

### 🔧 Cách Sử Dụng

1. **Tự động kích hoạt:** Các plugin sẽ tự động chạy khi trang load
2. **Hash-agnostic:** Hoạt động với bất kỳ CSS filename nào sau khi build
3. **Monitoring:** Check console để xem logs tối ưu hóa
4. **Testing:** Sử dụng Lighthouse để đo performance

### 🎯 Giải Pháp Cho CSS Hash Động

**Vấn đề:** CSS files được generate với hash khác nhau mỗi lần build:
- `/_nuxt/index.C42_iUI5.css` → `/_nuxt/index.XYZ789.css`
- `/_nuxt/button.B9v6vxHo.css` → `/_nuxt/button.ABC123.css`

**Giải pháp:**
```typescript
// ❌ Cách cũ - hardcode tên file
const criticalCSS = ['/_nuxt/index.C42_iUI5.css']

// ✅ Cách mới - pattern matching
const isCritical = href.includes('/index.') || href.includes('/entry.')
```

**Lợi ích:**
- ✅ Không cần update code sau mỗi lần build
- ✅ Tự động detect critical/non-critical CSS
- ✅ Hoạt động với bất kỳ hash nào
- ✅ Maintainable và scalable

### 📈 Monitoring & Debug

**Console logs để theo dõi:**
```javascript
// CSS Optimization
console.log('🚀 Deferred non-critical CSS:', cssFile)
console.log('📱 Loaded feature on demand:', featureName)

// Service Worker
console.log('[SW] Serving critical CSS from cache:', request.url)
console.log('[SW] Cached new CSS:', request.url)

// Resource Hints
console.log('🔗 Dynamically preloaded:', resource.href)
console.log('🌐 DNS prefetched:', domain)
```

### 🎯 Best Practices

1. **Critical CSS:** Chỉ inline CSS cho above-the-fold content
2. **Lazy Loading:** Load non-critical resources khi cần thiết
3. **Caching Strategy:** Sử dụng appropriate caching cho từng loại resource
4. **Connection Awareness:** Adapt loading strategy dựa trên network conditions
5. **Progressive Enhancement:** Đảm bảo trang vẫn hoạt động khi JS/CSS chưa load

### 🔄 Maintenance

- **Update critical CSS:** Khi thay đổi above-the-fold design
- **Review non-critical patterns:** Khi thêm CSS files mới
- **Monitor performance:** Định kỳ check Lighthouse scores
- **Update Service Worker:** Khi có breaking changes

### 📝 Notes

- Các tối ưu hóa này tương thích với Nuxt 3
- Service Worker cần HTTPS để hoạt động trong production
- Test thoroughly trên các devices và network conditions khác nhau
