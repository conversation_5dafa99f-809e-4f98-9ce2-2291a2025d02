# Performance Optimization Guide

## Render Blocking CSS Optimization

Dự án đã được tối ưu hóa để giải quyết vấn đề **Render Blocking Requests** với ước tính tiết kiệm **1,530ms**.

### 🎯 Vấn đề đã giải quyết

Các file CSS sau đây đã được tối ưu hóa để không còn block việc render trang:

- `/_nuxt/index.C42_iUI5.css` (41.9 KiB, 1,830ms)
- `/_nuxt/button.B9v6vxHo.css` (1.0 KiB, 480ms)  
- `/_nuxt/entry.ceyXRjo-.css` (48.3 KiB, 2,130ms)
- `/_nuxt/index.D2C6dFch.css` (0.9 KiB, 180ms)
- `/_nuxt/index.Dy9eSoo3.css` (0.7 KiB, 180ms)
- `/_nuxt/index.AJ5clg8T.css` (2.4 KiB, 180ms)
- `/_nuxt/default.D7SrQHRa.css` (0.9 KiB, 180ms)
- `/_nuxt/login-form.CYniJ5Ez.css` (0.7 KiB, 180ms)
- `/_nuxt/nuxt-icon.D08378P0.css` (0.5 KiB, 480ms)
- `/_nuxt/swiper-vue.D2zhFq2K.css`

### 🚀 Các Plugin Tối Ưu Hóa

#### 1. CSS Optimization Plugin (`plugins/css-optimization.client.ts`)

**Chức năng:**
- ✅ Inline critical CSS cho above-the-fold content
- ✅ Defer non-critical CSS files
- ✅ Progressive enhancement cho below-the-fold content
- ✅ Optimize font loading với `font-display: swap`
- ✅ Remove unused CSS (basic implementation)

**Cách hoạt động:**
```typescript
// Critical CSS được inline ngay lập tức
const criticalCSS = `
    body { margin: 0; padding: 0; font-family: 'Montserrat', sans-serif; }
    .hero-banner { position: relative; width: 100%; min-height: 400px; }
    .header { position: fixed; top: 0; z-index: 1000; }
`

// Non-critical CSS được defer
nonCriticalPatterns.forEach(pattern => {
    link.rel = 'preload'
    link.as = 'style'
    link.onload = function() {
        this.rel = 'stylesheet'
    }
})
```

#### 2. JavaScript Optimization Plugin (`plugins/js-optimization.client.ts`)

**Chức năng:**
- ✅ Defer non-critical JavaScript
- ✅ Lazy load third-party scripts
- ✅ Optimize inline scripts
- ✅ Preload critical JavaScript modules
- ✅ Code splitting optimization

**Cách hoạt động:**
```typescript
// Lazy load third-party scripts
const thirdPartyScripts = [
    {
        src: 'https://www.googletagmanager.com/gtag/js',
        condition: () => window.scrollY > 100
    }
]

// Load on user interaction
interactionEvents.forEach(event => {
    document.addEventListener(event, loadScripts, { once: true })
})
```

#### 3. Resource Hints Plugin (`plugins/resource-hints.client.ts`)

**Chức năng:**
- ✅ Dynamic preload cho critical resources
- ✅ Intelligent DNS prefetch
- ✅ Preconnect optimization với timing
- ✅ Module preload cho critical JavaScript
- ✅ Adaptive preloading dựa trên connection quality
- ✅ Intersection Observer cho lazy preloading

**Cách hoạt động:**
```typescript
// Adaptive preloading based on connection
const connection = navigator.connection
if (connection.effectiveType === 'slow-2g') {
    // Only preload critical resources
    preloadCriticalOnly()
} else {
    // Full preloading
    preloadAll()
}
```

#### 4. Service Worker Optimization (`public/sw.js`)

**Chức năng:**
- ✅ Aggressive caching cho CSS và JS files
- ✅ CSS Optimized Strategy - ưu tiên critical CSS
- ✅ Cache First cho static resources
- ✅ Stale While Revalidate cho non-critical resources

**Cách hoạt động:**
```javascript
// CSS Optimized Strategy
async function cssOptimizedStrategy(request) {
    const isCriticalCSS = url.pathname.includes('entry.css')
    
    if (isCriticalCSS) {
        // Cache first with immediate response
        return cacheFirstStrategy(request)
    } else {
        // Stale while revalidate
        return staleWhileRevalidateStrategy(request)
    }
}
```

### ⚙️ Cấu Hình Nuxt.config.ts

**Các thay đổi quan trọng:**

```typescript
experimental: {
    inlineSSRStyles: true, // Inline critical CSS
    treeshakeClientOnly: true, // Remove unused code
},

vitalizer: {
    disableStylesheets: 'entry', // Disable render blocking stylesheets
    disablePrefetchLinks: true,
},

build: {
    extractCSS: {
        chunkFilename: '_nuxt/[name].[contenthash:8].css' // Split CSS chunks
    },
    rollupOptions: {
        output: {
            assetFileNames: (assetInfo) => {
                if (assetInfo.name?.endsWith('.css')) {
                    return '_nuxt/css/[name].[hash][extname]'
                }
                return '_nuxt/[name].[hash][extname]'
            }
        }
    }
}
```

### 📊 Kết Quả Dự Kiến

**Trước khi tối ưu hóa:**
- Render blocking requests: 1,530ms
- LCP (Largest Contentful Paint): >4s
- FCP (First Contentful Paint): Chậm

**Sau khi tối ưu hóa:**
- ✅ Giảm render blocking time: ~1,530ms
- ✅ Cải thiện LCP: <2.5s (mục tiêu)
- ✅ Cải thiện FCP: <1.8s
- ✅ Tăng Performance Score: +20-30 điểm

### 🔧 Cách Sử Dụng

1. **Tự động kích hoạt:** Các plugin sẽ tự động chạy khi trang load
2. **Monitoring:** Check console để xem logs tối ưu hóa
3. **Testing:** Sử dụng Lighthouse để đo performance

### 📈 Monitoring & Debug

**Console logs để theo dõi:**
```javascript
// CSS Optimization
console.log('🚀 Deferred non-critical CSS:', cssFile)
console.log('📱 Loaded feature on demand:', featureName)

// Service Worker
console.log('[SW] Serving critical CSS from cache:', request.url)
console.log('[SW] Cached new CSS:', request.url)

// Resource Hints
console.log('🔗 Dynamically preloaded:', resource.href)
console.log('🌐 DNS prefetched:', domain)
```

### 🎯 Best Practices

1. **Critical CSS:** Chỉ inline CSS cho above-the-fold content
2. **Lazy Loading:** Load non-critical resources khi cần thiết
3. **Caching Strategy:** Sử dụng appropriate caching cho từng loại resource
4. **Connection Awareness:** Adapt loading strategy dựa trên network conditions
5. **Progressive Enhancement:** Đảm bảo trang vẫn hoạt động khi JS/CSS chưa load

### 🔄 Maintenance

- **Update critical CSS:** Khi thay đổi above-the-fold design
- **Review non-critical patterns:** Khi thêm CSS files mới
- **Monitor performance:** Định kỳ check Lighthouse scores
- **Update Service Worker:** Khi có breaking changes

### 📝 Notes

- Các tối ưu hóa này tương thích với Nuxt 3
- Service Worker cần HTTPS để hoạt động trong production
- Test thoroughly trên các devices và network conditions khác nhau
