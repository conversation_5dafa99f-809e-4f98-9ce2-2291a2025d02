export default defineNuxtPlugin(() => {
    if (import.meta.client) {
        // Dynamic CSS optimization for files with hash names
        const optimizeDynamicCSS = () => {
            console.log('🎯 Starting dynamic CSS optimization...')

            // 1. Identify and categorize CSS files
            const categorizeCSS = () => {
                const allStylesheets = document.querySelectorAll(
                    'link[rel="stylesheet"]'
                )
                const critical = []
                const nonCritical = []

                allStylesheets.forEach((link) => {
                    const href = link.getAttribute('href') || ''

                    // Critical CSS patterns (load immediately)
                    const isCritical =
                        href.includes('/entry.') ||
                        href.includes('/index.') ||
                        href.includes('/app.') ||
                        href.includes('/main.') ||
                        href.includes('/critical.')

                    // Non-critical CSS patterns (can be deferred)
                    const isNonCritical =
                        href.includes('swiper') ||
                        href.includes('icon') ||
                        href.includes('button') ||
                        href.includes('login') ||
                        href.includes('default') ||
                        href.includes('modal') ||
                        href.includes('tooltip') ||
                        href.includes('chart')

                    if (isCritical) {
                        critical.push({ element: link, href })
                    } else if (isNonCritical) {
                        nonCritical.push({ element: link, href })
                    }
                })

                console.log(`📊 Found ${critical.length} critical CSS files`)
                console.log(
                    `📊 Found ${nonCritical.length} non-critical CSS files`
                )

                return { critical, nonCritical }
            }

            // 2. Defer non-critical CSS
            const deferNonCriticalCSS = (nonCriticalFiles) => {
                nonCriticalFiles.forEach(({ element, href }) => {
                    // Convert to preload
                    element.setAttribute('rel', 'preload')
                    element.setAttribute('as', 'style')

                    // Add onload handler to convert back to stylesheet
                    element.setAttribute(
                        'onload',
                        "this.onload=null;this.rel='stylesheet'"
                    )

                    // Add noscript fallback
                    const noscript = document.createElement('noscript')
                    const fallback = document.createElement('link')
                    fallback.setAttribute('rel', 'stylesheet')
                    fallback.setAttribute('href', href)
                    noscript.appendChild(fallback)

                    if (element.parentNode) {
                        element.parentNode.insertBefore(
                            noscript,
                            element.nextSibling
                        )
                    }

                    console.log('🚀 Deferred CSS:', href)
                })
            }

            // 4. Progressive enhancement for below-the-fold content
            const progressiveEnhancement = () => {
                const observer = new IntersectionObserver(
                    (entries) => {
                        entries.forEach((entry) => {
                            if (entry.isIntersecting) {
                                const element = entry.target

                                // Show element with smooth transition
                                if (element && element.style) {
                                    element.style.opacity = '1'
                                }

                                observer.unobserve(element)
                            }
                        })
                    },
                    {
                        rootMargin: '50px 0px',
                        threshold: 0.1,
                    }
                )

                // Observe elements that should be shown progressively
                const selectors = [
                    '.swiper-container',
                    '[class*="swiper"]',
                    '.footer',
                    '[class*="footer"]',
                    '.modal',
                    '[class*="modal"]',
                    '.tooltip',
                    '[class*="tooltip"]',
                    '.game-section',
                    '[class*="game"]',
                    '.promotion',
                    '[class*="promotion"]',
                ]

                selectors.forEach((selector) => {
                    const elements = document.querySelectorAll(selector)
                    elements.forEach((element) => {
                        observer.observe(element)
                    })
                })

                console.log('👁️ Progressive enhancement observer set up')
            }

            // 5. Optimize font loading
            const optimizeFontLoading = () => {
                // Add font-display: swap to Google Fonts
                const fontLinks = document.querySelectorAll(
                    'link[href*="fonts.googleapis.com"]'
                )
                fontLinks.forEach((link) => {
                    const href = link.getAttribute('href') || ''
                    if (!href.includes('display=swap')) {
                        try {
                            const url = new URL(href)
                            url.searchParams.set('display', 'swap')
                            link.setAttribute('href', url.toString())
                            console.log('🔤 Optimized font loading:', href)
                        } catch (e) {
                            console.warn('Failed to optimize font URL:', href)
                        }
                    }
                })

                // Dynamically extract and preload critical font files
                const extractAndPreloadFonts = () => {
                    // Find the Google Fonts CSS link
                    const googleFontsLink = document.querySelector(
                        'link[href*="fonts.googleapis.com/css2"]'
                    )
                    if (googleFontsLink) {
                        const href = googleFontsLink.getAttribute('href')
                        if (href) {
                            // Fetch the CSS to extract actual font URLs
                            fetch(href)
                                .then((response) => response.text())
                                .then((css) => {
                                    // Extract woff2 URLs from the CSS
                                    const fontUrls = css.match(
                                        /https:\/\/fonts\.gstatic\.com\/[^)]+\.woff2/g
                                    )
                                    if (fontUrls) {
                                        // Preload the first 2 critical font files (regular and medium weight)
                                        fontUrls
                                            .slice(0, 2)
                                            .forEach((fontUrl) => {
                                                const existing =
                                                    document.querySelector(
                                                        `link[href="${fontUrl}"]`
                                                    )
                                                if (!existing) {
                                                    const link =
                                                        document.createElement(
                                                            'link'
                                                        )
                                                    link.rel = 'preload'
                                                    link.as = 'font'
                                                    link.type = 'font/woff2'
                                                    link.crossOrigin =
                                                        'anonymous'
                                                    link.href = fontUrl
                                                    document.head.appendChild(
                                                        link
                                                    )
                                                    console.log(
                                                        '🔤 Preloaded critical font:',
                                                        fontUrl
                                                    )
                                                }
                                            })
                                    }
                                })
                                .catch((error) => {
                                    console.warn(
                                        'Failed to extract font URLs:',
                                        error
                                    )
                                })
                        }
                    }
                }

                extractAndPreloadFonts()
            }

            // 6. Monitor and report performance
            const monitorPerformance = () => {
                if ('PerformanceObserver' in window) {
                    const observer = new PerformanceObserver((list) => {
                        list.getEntries().forEach((entry) => {
                            if (
                                entry.entryType === 'largest-contentful-paint'
                            ) {
                                const lcp = entry.startTime
                                console.log(
                                    '🎯 LCP after CSS optimization:',
                                    lcp.toFixed(2),
                                    'ms'
                                )

                                if (lcp > 2500) {
                                    console.warn(
                                        '⚠️ LCP still needs improvement'
                                    )
                                } else {
                                    console.log('✅ LCP is good!')
                                }
                            }
                        })
                    })
                    observer.observe({
                        entryTypes: ['largest-contentful-paint'],
                    })
                }
            }

            // Execute optimization steps
            const { nonCritical } = categorizeCSS()

            // Defer non-critical CSS
            if (nonCritical.length > 0) {
                deferNonCriticalCSS(nonCritical)
            }

            // Optimize fonts
            optimizeFontLoading()

            // Set up progressive enhancement
            setTimeout(() => {
                progressiveEnhancement()
            }, 100)

            // Monitor performance
            monitorPerformance()

            console.log('✅ Dynamic CSS optimization completed')
        }

        // Execute optimization
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', optimizeDynamicCSS)
        } else {
            optimizeDynamicCSS()
        }

        // Re-run on route changes (for SPA navigation)
        const router = useRouter()
        router.afterEach(() => {
            setTimeout(optimizeDynamicCSS, 100)
        })

        console.log('🚀 Dynamic CSS optimizer plugin loaded')
    }
})
