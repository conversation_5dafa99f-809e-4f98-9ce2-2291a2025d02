export default defineNuxtPlugin(() => {
    if (import.meta.client) {
        // Dynamic CSS optimization for files with hash names
        const optimizeDynamicCSS = () => {
            console.log('🎯 Starting dynamic CSS optimization...')

            // 1. Identify and categorize CSS files
            const categorizeCSS = () => {
                const allStylesheets = document.querySelectorAll('link[rel="stylesheet"]')
                const critical = []
                const nonCritical = []

                allStylesheets.forEach(link => {
                    const href = link.getAttribute('href') || ''
                    
                    // Critical CSS patterns (load immediately)
                    const isCritical = href.includes('/entry.') ||
                                     href.includes('/index.') ||
                                     href.includes('/app.') ||
                                     href.includes('/main.') ||
                                     href.includes('/critical.')

                    // Non-critical CSS patterns (can be deferred)
                    const isNonCritical = href.includes('swiper') ||
                                         href.includes('icon') ||
                                         href.includes('button') ||
                                         href.includes('login') ||
                                         href.includes('default') ||
                                         href.includes('modal') ||
                                         href.includes('tooltip') ||
                                         href.includes('chart')

                    if (isCritical) {
                        critical.push({ element: link, href })
                    } else if (isNonCritical) {
                        nonCritical.push({ element: link, href })
                    }
                })

                console.log(`📊 Found ${critical.length} critical CSS files`)
                console.log(`📊 Found ${nonCritical.length} non-critical CSS files`)

                return { critical, nonCritical }
            }

            // 2. Defer non-critical CSS
            const deferNonCriticalCSS = (nonCriticalFiles) => {
                nonCriticalFiles.forEach(({ element, href }) => {
                    // Convert to preload
                    element.setAttribute('rel', 'preload')
                    element.setAttribute('as', 'style')
                    
                    // Add onload handler to convert back to stylesheet
                    element.setAttribute('onload', "this.onload=null;this.rel='stylesheet'")
                    
                    // Add noscript fallback
                    const noscript = document.createElement('noscript')
                    const fallback = document.createElement('link')
                    fallback.setAttribute('rel', 'stylesheet')
                    fallback.setAttribute('href', href)
                    noscript.appendChild(fallback)
                    
                    if (element.parentNode) {
                        element.parentNode.insertBefore(noscript, element.nextSibling)
                    }
                    
                    console.log('🚀 Deferred CSS:', href)
                })
            }

            // 3. Inline critical CSS for above-the-fold content
            const inlineCriticalCSS = () => {
                const criticalCSS = `
                    /* Critical inline CSS for immediate rendering */
                    body { 
                        margin: 0; 
                        padding: 0; 
                        font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, sans-serif;
                        line-height: 1.6;
                    }
                    
                    .header, [class*="header"] { 
                        position: relative;
                        z-index: 1000; 
                    }
                    
                    .hero-banner, [class*="hero"], [class*="banner"] { 
                        position: relative; 
                        width: 100%; 
                        min-height: 300px;
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    }
                    
                    .hero-banner img, [class*="hero"] img { 
                        width: 100%; 
                        height: auto; 
                        display: block;
                        object-fit: cover;
                    }
                    
                    /* Hide non-critical elements initially */
                    .swiper-container, [class*="swiper"],
                    .footer, [class*="footer"],
                    .modal, [class*="modal"],
                    .tooltip, [class*="tooltip"] { 
                        opacity: 0; 
                        transition: opacity 0.3s ease-in-out; 
                    }
                    
                    /* Loading states */
                    .loading, [class*="loading"] {
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        min-height: 200px;
                    }
                    
                    /* Mobile optimizations */
                    @media (max-width: 768px) {
                        .hero-banner, [class*="hero"] { 
                            min-height: 250px; 
                        }
                    }
                `

                // Check if critical CSS is already inlined
                const existingCritical = document.querySelector('#dynamic-critical-css')
                if (!existingCritical) {
                    const style = document.createElement('style')
                    style.id = 'dynamic-critical-css'
                    style.textContent = criticalCSS
                    document.head.insertBefore(style, document.head.firstChild)
                    console.log('✅ Inlined critical CSS')
                }
            }

            // 4. Progressive enhancement for below-the-fold content
            const progressiveEnhancement = () => {
                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            const element = entry.target
                            
                            // Show element with smooth transition
                            if (element && element.style) {
                                element.style.opacity = '1'
                            }
                            
                            observer.unobserve(element)
                        }
                    })
                }, {
                    rootMargin: '50px 0px',
                    threshold: 0.1
                })

                // Observe elements that should be shown progressively
                const selectors = [
                    '.swiper-container', '[class*="swiper"]',
                    '.footer', '[class*="footer"]',
                    '.modal', '[class*="modal"]',
                    '.tooltip', '[class*="tooltip"]',
                    '.game-section', '[class*="game"]',
                    '.promotion', '[class*="promotion"]'
                ]

                selectors.forEach(selector => {
                    const elements = document.querySelectorAll(selector)
                    elements.forEach(element => {
                        observer.observe(element)
                    })
                })

                console.log('👁️ Progressive enhancement observer set up')
            }

            // 5. Optimize font loading
            const optimizeFontLoading = () => {
                // Add font-display: swap to Google Fonts
                const fontLinks = document.querySelectorAll('link[href*="fonts.googleapis.com"]')
                fontLinks.forEach(link => {
                    const href = link.getAttribute('href') || ''
                    if (!href.includes('display=swap')) {
                        try {
                            const url = new URL(href)
                            url.searchParams.set('display', 'swap')
                            link.setAttribute('href', url.toString())
                            console.log('🔤 Optimized font loading:', href)
                        } catch (e) {
                            console.warn('Failed to optimize font URL:', href)
                        }
                    }
                })

                // Preload critical font files
                const criticalFonts = [
                    'https://fonts.gstatic.com/s/montserrat/v25/JTUSjIg1_i6t8kCHKm459Wlhyw.woff2'
                ]

                criticalFonts.forEach(fontUrl => {
                    const existing = document.querySelector(`link[href="${fontUrl}"]`)
                    if (!existing) {
                        const link = document.createElement('link')
                        link.rel = 'preload'
                        link.as = 'font'
                        link.type = 'font/woff2'
                        link.crossOrigin = 'anonymous'
                        link.href = fontUrl
                        document.head.appendChild(link)
                        console.log('🔤 Preloaded critical font:', fontUrl)
                    }
                })
            }

            // 6. Monitor and report performance
            const monitorPerformance = () => {
                if ('PerformanceObserver' in window) {
                    const observer = new PerformanceObserver((list) => {
                        list.getEntries().forEach(entry => {
                            if (entry.entryType === 'largest-contentful-paint') {
                                const lcp = entry.startTime
                                console.log('🎯 LCP after CSS optimization:', lcp.toFixed(2), 'ms')
                                
                                if (lcp > 2500) {
                                    console.warn('⚠️ LCP still needs improvement')
                                } else {
                                    console.log('✅ LCP is good!')
                                }
                            }
                        })
                    })
                    observer.observe({ entryTypes: ['largest-contentful-paint'] })
                }
            }

            // Execute optimization steps
            const { critical, nonCritical } = categorizeCSS()
            
            // Inline critical CSS first
            inlineCriticalCSS()
            
            // Defer non-critical CSS
            if (nonCritical.length > 0) {
                deferNonCriticalCSS(nonCritical)
            }
            
            // Optimize fonts
            optimizeFontLoading()
            
            // Set up progressive enhancement
            setTimeout(() => {
                progressiveEnhancement()
            }, 100)
            
            // Monitor performance
            monitorPerformance()
            
            console.log('✅ Dynamic CSS optimization completed')
        }

        // Execute optimization
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', optimizeDynamicCSS)
        } else {
            optimizeDynamicCSS()
        }

        // Re-run on route changes (for SPA navigation)
        const router = useRouter()
        router.afterEach(() => {
            setTimeout(optimizeDynamicCSS, 100)
        })

        console.log('🚀 Dynamic CSS optimizer plugin loaded')
    }
})
