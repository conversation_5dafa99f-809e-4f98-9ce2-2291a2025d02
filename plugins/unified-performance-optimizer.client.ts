export default defineNuxtPlugin(() => {
    if (import.meta.client) {
        // Unified Performance Optimizer - All optimizations in one place
        const unifiedPerformanceOptimizer = () => {
            console.log('🚀 Starting unified performance optimization...')

            // 1. RESOURCE DETECTION & ANALYSIS
            const detectResources = () => {
                const resources = {
                    css: { critical: [], nonCritical: [], unknown: [] },
                    js: { critical: [], nonCritical: [], unknown: [] },
                    images: { hero: [], thumbnails: [], icons: [] },
                    fonts: [],
                    cache: { images: [], scripts: [], stylesheets: [] },
                }

                // Detect CSS files
                document
                    .querySelectorAll('link[rel="stylesheet"]')
                    ?.forEach((link) => {
                        const href = link.getAttribute('href') || ''
                        if (href.includes('/_nuxt/')) {
                            if (
                                href.includes('entry') ||
                                href.includes('app')
                            ) {
                                resources.css.critical.push(href)
                            } else {
                                resources.css.nonCritical.push(href)
                            }
                        }
                    })

                // Detect JS files
                document.querySelectorAll('script[src]')?.forEach((script) => {
                    const src = script.getAttribute('src') || ''
                    if (src.includes('/_nuxt/')) {
                        if (
                            src.includes('entry') ||
                            src.includes('app') ||
                            src.includes('runtime')
                        ) {
                            resources.js.critical.push(src)
                        } else {
                            resources.js.nonCritical.push(src)
                        }
                    }
                })

                // Detect images for cache optimization
                document.querySelectorAll('img[src]')?.forEach((img) => {
                    const src = img.getAttribute('src') || ''
                    if (src.startsWith('/assets/')) {
                        if (src.includes('/hero-banner/')) {
                            resources.images.hero.push({
                                src,
                                element: img,
                                size: getEstimatedSize(src),
                            })
                        } else {
                            resources.images.thumbnails.push({
                                src,
                                element: img,
                                size: getEstimatedSize(src),
                            })
                        }
                        resources.cache.images.push({
                            src,
                            element: img,
                            size: getEstimatedSize(src),
                        })
                    }
                })

                console.log('🔍 Resources detected:', resources)
                return resources
            }

            // 2. JAVASCRIPT OPTIMIZATION
            const optimizeJavaScript = () => {
                // Defer non-critical scripts
                const nonCriticalPatterns = [
                    'analytics',
                    'gtm',
                    'facebook',
                    'twitter',
                    'social',
                    'chat',
                ]

                document.querySelectorAll('script[src]')?.forEach((script) => {
                    const src = script.getAttribute('src') || ''
                    const isNonCritical = nonCriticalPatterns.some((pattern) =>
                        src.includes(pattern)
                    )

                    if (
                        isNonCritical &&
                        !script.hasAttribute('defer') &&
                        !script.hasAttribute('async')
                    ) {
                        script.setAttribute('defer', 'true')
                        script.setAttribute('fetchpriority', 'low')
                        console.log('🚀 Deferred non-critical script:', src)
                    }
                })

                // Optimize Facebook SDK (remove legacy polyfills)
                const optimizeFacebookSDK = () => {
                    if (
                        !window.fbq &&
                        !document.querySelector('script[src*="fbevents"]')
                    ) {
                        // Modern Facebook Pixel stub
                        window.fbq = function (...args) {
                            ;(window.fbq.q = window.fbq.q || []).push(args)
                        }
                        window.fbq.loaded = true
                        window.fbq.version = '2.0'

                        // Load optimized version on interaction
                        const loadOptimizedFB = () => {
                            const script = document.createElement('script')
                            script.src =
                                'https://connect.facebook.net/en_US/fbevents.js'
                            script.async = true
                            script.defer = true
                            script.setAttribute('fetchpriority', 'low')
                            document.head.appendChild(script)
                            console.log('📘 Loaded optimized Facebook SDK')
                        }

                        // Load after user interaction or delay
                        setTimeout(loadOptimizedFB, 3000)[
                            ('scroll', 'click', 'touchstart')
                        ]?.forEach((event) => {
                            document.addEventListener(event, loadOptimizedFB, {
                                once: true,
                                passive: true,
                            })
                        })
                    }
                }

                optimizeFacebookSDK()
            }

            // 3. CSS OPTIMIZATION
            const optimizeCSS = () => {
                // Preload critical CSS
                const criticalCSSPatterns = ['entry', 'app', 'main']

                document
                    .querySelectorAll('link[rel="stylesheet"]')
                    ?.forEach((link) => {
                        const href = link.getAttribute('href') || ''
                        const isCritical = criticalCSSPatterns.some((pattern) =>
                            href.includes(pattern)
                        )

                        if (
                            isCritical &&
                            !document.querySelector(
                                `link[rel="preload"][href="${href}"]`
                            )
                        ) {
                            const preloadLink = document.createElement('link')
                            preloadLink.rel = 'preload'
                            preloadLink.as = 'style'
                            preloadLink.href = href
                            preloadLink.setAttribute('fetchpriority', 'high')
                            document.head.insertBefore(
                                preloadLink,
                                document.head.firstChild
                            )
                            console.log('🎨 Preloaded critical CSS:', href)
                        }
                    })

                // Remove unused CSS (basic detection)
                const removeUnusedCSS = () => {
                    const stylesheets = document.querySelectorAll(
                        'link[rel="stylesheet"]'
                    )
                    stylesheets?.forEach((link) => {
                        const href = link.getAttribute('href') || ''
                        // Mark non-critical CSS for lazy loading
                        if (href.includes('vendor') || href.includes('chunk')) {
                            link.setAttribute('media', 'print')
                            link.setAttribute('onload', "this.media='all'")
                            console.log('🎨 Made CSS non-blocking:', href)
                        }
                    })
                }

                removeUnusedCSS()
            }

            // 4. RESOURCE HINTS OPTIMIZATION
            const optimizeResourceHints = () => {
                // DNS prefetch for external domains
                const externalDomains = [
                    '//fonts.googleapis.com',
                    '//fonts.gstatic.com',
                    '//www.googletagmanager.com',
                    '//connect.facebook.net',
                ]

                externalDomains?.forEach((domain) => {
                    if (
                        !document.querySelector(
                            `link[rel="dns-prefetch"][href="${domain}"]`
                        )
                    ) {
                        const link = document.createElement('link')
                        link.rel = 'dns-prefetch'
                        link.href = domain
                        document.head.appendChild(link)
                        console.log('🌐 Added DNS prefetch:', domain)
                    }
                })

                // Preconnect to critical domains
                const criticalDomains = [
                    { href: 'https://fonts.googleapis.com', crossorigin: true },
                    { href: 'https://fonts.gstatic.com', crossorigin: true },
                ]

                criticalDomains?.forEach(({ href, crossorigin }) => {
                    if (
                        !document.querySelector(
                            `link[rel="preconnect"][href="${href}"]`
                        )
                    ) {
                        const link = document.createElement('link')
                        link.rel = 'preconnect'
                        link.href = href
                        if (crossorigin)
                            link.setAttribute('crossorigin', 'anonymous')
                        document.head.appendChild(link)
                        console.log('🔗 Added preconnect:', href)
                    }
                })
            }

            // 5. LCP OPTIMIZATION
            const optimizeLCP = () => {
                const { isMobile } = useDevice()
                const staticUrl = useRuntimeConfig().public.staticUrl

                // Preload hero images for LCP
                const heroImages = {
                    desktop: `${staticUrl}/home/<USER>/jackpot/pc.avif`,
                    mobile: `${staticUrl}/home/<USER>/jackpot/mb.avif`,
                }

                const currentHeroImage = isMobile
                    ? heroImages.mobile
                    : heroImages.desktop
                const mediaQuery = isMobile
                    ? '(max-width: 991px)'
                    : '(min-width: 992px)'

                if (
                    !document.querySelector(`link[href="${currentHeroImage}"]`)
                ) {
                    const link = document.createElement('link')
                    link.rel = 'preload'
                    link.as = 'image'
                    link.type = 'image/avif'
                    link.href = currentHeroImage
                    link.media = mediaQuery
                    link.setAttribute('fetchpriority', 'high')
                    document.head.insertBefore(link, document.head.firstChild)
                    console.log(
                        '🎯 Preloaded LCP hero image:',
                        currentHeroImage
                    )
                }

                // Ensure hero images are not lazy loaded
                document
                    .querySelectorAll('img[src*="hero-banner"]')
                    ?.forEach((img, index) => {
                        if (index === 0) {
                            // First hero image
                            img.setAttribute('loading', 'eager')
                            img.setAttribute('fetchpriority', 'high')
                            img.setAttribute('data-lcp-candidate', 'true')
                            console.log('🎯 Optimized LCP image:', img.src)
                        }
                    })
            }

            // 6. CACHE OPTIMIZATION
            const optimizeCache = (resources) => {
                // Add preload hints for large assets
                const largeAssets = [
                    ...resources.cache.images.filter((img) => img.size > 100),
                    ...resources.cache.scripts.filter(
                        (script) => script.size > 50
                    ),
                ]

                largeAssets?.slice(0, 3)?.forEach((asset) => {
                    if (
                        !document.querySelector(
                            `link[href="${asset.src}"][rel="preload"]`
                        )
                    ) {
                        const link = document.createElement('link')
                        link.rel = 'preload'
                        link.as = asset.src.includes('.js') ? 'script' : 'image'
                        link.href = asset.src
                        if (asset.src.includes('.avif'))
                            link.type = 'image/avif'
                        if (asset.src.includes('.webp'))
                            link.type = 'image/webp'
                        link.setAttribute('fetchpriority', 'high')
                        document.head.appendChild(link)
                        console.log(
                            '🗂️ Preloaded large asset:',
                            asset.src,
                            `(~${asset.size}KB)`
                        )
                    }
                })

                // Optimize lazy loading for non-critical images
                resources.cache.images?.forEach((img) => {
                    if (!img.src.includes('hero-banner') && img.size > 50) {
                        const element = img.element
                        if (!element.hasAttribute('loading')) {
                            element.setAttribute('loading', 'lazy')
                            element.setAttribute('decoding', 'async')
                            console.log('⏰ Added lazy loading to:', img.src)
                        }
                    }
                })
            }

            // Helper function to estimate file size
            const getEstimatedSize = (src) => {
                // Known large files from performance reports
                if (src.includes('danh-de-mien-phi/pc.png')) return 612
                if (src.includes('club-world-cup/pc.jpg')) return 365
                if (src.includes('golden-star-warrios/pc.jpg')) return 219
                if (src.includes('jackpot/pc.jpg')) return 157
                if (src.includes('nanoplayer.4.min.js')) return 118

                // Default estimates by file type
                if (src.includes('.png')) return 100
                if (src.includes('.jpg')) return 80
                if (src.includes('.webp')) return 60
                if (src.includes('.avif')) return 40
                if (src.includes('.js')) return 50
                if (src.includes('.css')) return 20

                return 10
            }

            // 7. PERFORMANCE MONITORING
            const monitorPerformance = () => {
                if ('PerformanceObserver' in window) {
                    // Monitor LCP
                    const lcpObserver = new PerformanceObserver((list) => {
                        list?.getEntries()?.forEach((entry) => {
                            if (
                                entry.entryType === 'largest-contentful-paint'
                            ) {
                                const lcp = entry.startTime
                                console.log(`🎯 LCP: ${lcp.toFixed(2)}ms`)

                                if (lcp > 2500) {
                                    console.warn('⚠️ LCP is slow (>2.5s)')
                                } else {
                                    console.log('✅ LCP is good! (<2.5s)')
                                }
                            }
                        })
                    })
                    lcpObserver.observe({
                        entryTypes: ['largest-contentful-paint'],
                    })

                    // Monitor resource loading
                    const resourceObserver = new PerformanceObserver((list) => {
                        list?.getEntries()?.forEach((entry) => {
                            if (
                                entry.name.includes('/assets/') ||
                                entry.name.includes('/_nuxt/')
                            ) {
                                const duration = entry.duration || 0
                                const transferSize = entry.transferSize || 0

                                if (duration > 500 && transferSize > 50000) {
                                    console.warn('🐌 Slow loading asset:', {
                                        name: entry.name,
                                        duration: `${duration.toFixed(2)}ms`,
                                        size: `${Math.round(
                                            transferSize / 1024
                                        )}KB`,
                                    })
                                }
                            }
                        })
                    })
                    resourceObserver.observe({ entryTypes: ['resource'] })
                }
            }

            // 8. GENERATE OPTIMIZATION REPORT
            const generateReport = (resources) => {
                const report = {
                    timestamp: new Date().toISOString(),
                    optimizations: {
                        jsOptimized: true,
                        cssOptimized: true,
                        lcpOptimized: true,
                        cacheOptimized: true,
                        resourceHintsAdded: true,
                        performanceMonitored: true,
                    },
                    resources: {
                        totalCSS:
                            resources.css.critical.length +
                            resources.css.nonCritical.length,
                        totalJS:
                            resources.js.critical.length +
                            resources.js.nonCritical.length,
                        totalImages: resources.cache.images.length,
                        heroImages: resources.images.hero.length,
                    },
                    recommendations: [
                        'Monitor LCP scores regularly',
                        'Consider removing unused CSS/JS',
                        'Optimize large images further',
                        'Use modern image formats (AVIF/WebP)',
                    ],
                }

                console.log('📊 Unified Performance Optimization Report:')
                console.table(report.optimizations)
                console.table(report.resources)

                // Store for debugging
                window.__PERFORMANCE_REPORT__ = report

                return report
            }

            // EXECUTE ALL OPTIMIZATIONS
            const executeOptimizations = () => {
                const resources = detectResources()

                optimizeJavaScript()
                optimizeCSS()
                optimizeResourceHints()
                optimizeLCP()
                optimizeCache(resources)
                monitorPerformance()

                const report = generateReport(resources)

                console.log('✅ Unified performance optimization completed')
                return report
            }

            return executeOptimizations()
        }

        // Run optimization when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener(
                'DOMContentLoaded',
                unifiedPerformanceOptimizer
            )
        } else {
            unifiedPerformanceOptimizer()
        }

        // Re-run on route changes
        const router = useRouter()
        router.afterEach(() => {
            setTimeout(unifiedPerformanceOptimizer, 300)
        })

        console.log('🚀 Unified performance optimizer plugin loaded')
    }
})
