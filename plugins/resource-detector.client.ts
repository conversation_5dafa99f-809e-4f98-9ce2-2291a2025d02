export default defineNuxtPlugin(() => {
    if (import.meta.client) {
        // Resource detector for dynamically generated assets
        const detectAndOptimizeResources = () => {
            console.log('🔍 Starting resource detection...')

            // 1. Detect all CSS files in the DOM
            const detectCSSFiles = () => {
                const cssFiles = {
                    critical: [],
                    nonCritical: [],
                    unknown: [],
                }

                const allStylesheets = document.querySelectorAll(
                    'link[rel="stylesheet"]'
                )
                allStylesheets.forEach((link) => {
                    const href = link.getAttribute('href') || ''

                    if (href.includes('/_nuxt/')) {
                        // Critical CSS patterns
                        if (
                            href.includes('/entry.') ||
                            href.includes('/index.') ||
                            href.includes('/app.') ||
                            href.includes('/main.')
                        ) {
                            cssFiles.critical.push(href)
                        }
                        // Non-critical CSS patterns
                        else if (
                            href.includes('swiper') ||
                            href.includes('icon') ||
                            href.includes('button') ||
                            href.includes('login') ||
                            href.includes('default') ||
                            href.includes('modal')
                        ) {
                            cssFiles.nonCritical.push(href)
                        }
                        // Unknown Nuxt CSS
                        else {
                            cssFiles.unknown.push(href)
                        }
                    }
                })

                console.log('📊 CSS Detection Results:')
                console.log('  Critical CSS:', cssFiles.critical)
                console.log('  Non-critical CSS:', cssFiles.nonCritical)
                console.log('  Unknown CSS:', cssFiles.unknown)

                return cssFiles
            }

            // 2. Detect all JS files in the DOM
            const detectJSFiles = () => {
                const jsFiles = {
                    critical: [],
                    nonCritical: [],
                    unknown: [],
                }

                const allScripts = document.querySelectorAll('script[src]')
                allScripts.forEach((script) => {
                    const src = script.getAttribute('src') || ''

                    if (src.includes('/_nuxt/')) {
                        // Critical JS patterns
                        if (
                            src.includes('/entry.') ||
                            src.includes('/app.') ||
                            src.includes('/runtime.') ||
                            src.includes('/main.')
                        ) {
                            jsFiles.critical.push(src)
                        }
                        // Non-critical JS patterns
                        else if (
                            src.includes('chunk') ||
                            src.includes('vendor') ||
                            src.includes('lazy') ||
                            src.includes('async')
                        ) {
                            jsFiles.nonCritical.push(src)
                        }
                        // Unknown Nuxt JS
                        else {
                            jsFiles.unknown.push(src)
                        }
                    }
                })

                console.log('📊 JS Detection Results:')
                console.log('  Critical JS:', jsFiles.critical)
                console.log('  Non-critical JS:', jsFiles.nonCritical)
                console.log('  Unknown JS:', jsFiles.unknown)

                return jsFiles
            }

            // 3. Detect font files from Google Fonts CSS
            const detectFontFiles = async () => {
                const fontFiles = []

                const googleFontsLinks = document.querySelectorAll(
                    'link[href*="fonts.googleapis.com/css"]'
                )

                for (const link of googleFontsLinks) {
                    const href = link.getAttribute('href')
                    if (href) {
                        try {
                            const response = await fetch(href)
                            const css = await response.text()

                            // Extract all font URLs
                            const fontUrls = css.match(
                                /https:\/\/fonts\.gstatic\.com\/[^)]+\.(woff2|woff|ttf)/g
                            )
                            if (fontUrls) {
                                fontFiles.push(...fontUrls)
                            }
                        } catch (error) {
                            console.warn(
                                'Failed to fetch font CSS:',
                                href,
                                error
                            )
                        }
                    }
                }

                console.log('📊 Font Detection Results:')
                console.log('  Font files:', fontFiles)

                return fontFiles
            }

            // 4. Detect image resources
            const detectImageResources = () => {
                const images = {
                    critical: [],
                    lazy: [],
                }

                // Critical images (hero banners, above-the-fold)
                const criticalSelectors = [
                    '.hero-banner img',
                    '[class*="hero"] img',
                    '.header img',
                    '[class*="banner"] img',
                ]

                criticalSelectors.forEach((selector) => {
                    const imgs = document.querySelectorAll(selector)
                    imgs.forEach((img) => {
                        const src =
                            img.getAttribute('src') ||
                            img.getAttribute('data-src')
                        if (src && src.includes('/assets/images/')) {
                            images.critical.push(src)
                        }
                    })
                })

                // Lazy images (below-the-fold)
                const lazyImages = document.querySelectorAll(
                    'img[loading="lazy"], img[data-src]'
                )
                lazyImages.forEach((img) => {
                    const src =
                        img.getAttribute('src') || img.getAttribute('data-src')
                    if (src && src.includes('/assets/images/')) {
                        images.lazy.push(src)
                    }
                })

                console.log('📊 Image Detection Results:')
                console.log('  Critical images:', images.critical)
                console.log('  Lazy images:', images.lazy)

                return images
            }

            // 5. Generate optimization recommendations
            const generateRecommendations = (detectedResources) => {
                const recommendations = []

                // CSS recommendations
                if (detectedResources.css.critical.length > 0) {
                    recommendations.push({
                        type: 'CSS_CRITICAL',
                        action: 'Preload these critical CSS files',
                        resources: detectedResources.css.critical,
                        priority: 'high',
                    })
                }

                if (detectedResources.css.nonCritical.length > 0) {
                    recommendations.push({
                        type: 'CSS_NON_CRITICAL',
                        action: 'Defer these non-critical CSS files',
                        resources: detectedResources.css.nonCritical,
                        priority: 'medium',
                    })
                }

                // JS recommendations
                if (detectedResources.js.critical.length > 0) {
                    recommendations.push({
                        type: 'JS_CRITICAL',
                        action: 'Preload these critical JS modules',
                        resources: detectedResources.js.critical,
                        priority: 'high',
                    })
                }

                // Font recommendations
                if (detectedResources.fonts.length > 0) {
                    recommendations.push({
                        type: 'FONTS',
                        action: 'Preload first 2 critical font files',
                        resources: detectedResources.fonts.slice(0, 2),
                        priority: 'high',
                    })
                }

                // Image recommendations
                if (detectedResources.images.critical.length > 0) {
                    recommendations.push({
                        type: 'IMAGES_CRITICAL',
                        action: 'Preload critical hero images',
                        resources: detectedResources.images.critical.slice(
                            0,
                            2
                        ),
                        priority: 'high',
                    })
                }

                console.log('💡 Optimization Recommendations:')
                recommendations.forEach((rec) => {
                    console.log(`  ${rec.type}: ${rec.action}`)
                    console.log(`    Resources:`, rec.resources)
                })

                return recommendations
            }

            // 6. Store detected resources for other plugins
            const storeDetectedResources = (resources) => {
                // Store in window object for other plugins to access
                window.__DETECTED_RESOURCES__ = resources

                // Dispatch custom event
                const event = new CustomEvent('resourcesDetected', {
                    detail: resources,
                })
                document.dispatchEvent(event)
            }

            // Execute detection
            const runDetection = async () => {
                const css = detectCSSFiles()
                const js = detectJSFiles()
                const fonts = await detectFontFiles()
                const images = detectImageResources()

                const detectedResources = { css, js, fonts, images }
                const recommendations =
                    generateRecommendations(detectedResources)

                storeDetectedResources({
                    ...detectedResources,
                    recommendations,
                })

                console.log('✅ Resource detection completed')
                return detectedResources
            }

            return runDetection()
        }

        // Run detection when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener(
                'DOMContentLoaded',
                detectAndOptimizeResources
            )
        } else {
            detectAndOptimizeResources()
        }

        // Re-run on route changes
        const router = useRouter()
        router.afterEach(() => {
            setTimeout(detectAndOptimizeResources, 200)
        })

        console.log('🔍 Resource detector plugin loaded')
    }
})
