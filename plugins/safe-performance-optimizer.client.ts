export default defineNuxtPlugin(() => {
    // Safe performance optimization
    const safeOptimizePerformance = () => {
        // 1. Safe JavaScript optimization - only defer non-critical scripts
        const safeOptimizeJavaScript = () => {
            // Only defer scripts that are not critical for initial render
            const nonCriticalScripts = document.querySelectorAll(
                'script[src*="analytics"], script[src*="gtm"], script[src*="facebook"]'
            )

            nonCriticalScripts.forEach((script) => {
                if (
                    !script.hasAttribute('defer') &&
                    !script.hasAttribute('async')
                ) {
                    script.setAttribute('defer', 'true')
                }
            })
        }

        // 2. Enhanced CSS optimization - handle render blocking CSS
        const safeOptimizeCSS = () => {
            // Method 1: Optimize all CSS files from current domain
            const externalStyleSheets = document.querySelectorAll(
                'link[rel="stylesheet"][href*="googleapis.com"], link[rel="stylesheet"][href*="gstatic.com"]'
            )

            externalStyleSheets.forEach((sheet) => {
                if (!sheet.hasAttribute('media')) {
                    sheet.setAttribute('media', 'print')
                    sheet.setAttribute('onload', "this.media='all'")
                }
            })
        }

        // 3. Safe resource preloading - only preload critical hero banner images
        const safePreloadResources = () => {
            // Only preload the most critical hero banner images
            const criticalImages = [
                '/assets/images/home/<USER>/jackpot/pc.avif',
                '/assets/images/home/<USER>/jackpot/pc.webp',
                '/assets/images/home/<USER>/jackpot/pc.jpg',
                '/assets/images/home/<USER>/jackpot/mb.avif',
                '/assets/images/home/<USER>/jackpot/mb.webp',
                '/assets/images/home/<USER>/jackpot/mb.jpg',
            ]

            criticalImages.forEach((src) => {
                // Check if preload link already exists
                const existingLink = document.querySelector(
                    `link[href="${src}"]`
                )
                if (!existingLink) {
                    const link = document.createElement('link')
                    link.rel = 'preload'
                    link.as = 'image'
                    link.href = src
                    link.type = src.includes('.avif')
                        ? 'image/avif'
                        : 'image/webp'
                    document.head.appendChild(link)
                }
            })
        }

        // 4. Safe DNS prefetch - only for external domains
        const safeDNSPrefetch = () => {
            const externalDomains = [
                'https://fonts.googleapis.com',
                'https://fonts.gstatic.com',
            ]

            externalDomains.forEach((domain) => {
                // Check if DNS prefetch already exists
                const existingLink = document.querySelector(
                    `link[href="${domain}"]`
                )
                if (!existingLink) {
                    const link = document.createElement('link')
                    link.rel = 'dns-prefetch'
                    link.href = domain
                    document.head.appendChild(link)
                }
            })
        }

        // 5. Enhanced performance monitoring with CSS loading tracking
        const safeMonitorPerformance = () => {
            if ('PerformanceObserver' in window) {
                // Monitor LCP without interfering
                const lcpObserver = new PerformanceObserver((list) => {
                    for (const entry of list.getEntries()) {
                        if (entry.entryType === 'largest-contentful-paint') {
                            console.log('🎯 LCP:', entry.startTime, 'ms')

                            // Log recommendations without modifying UI
                            if (entry.startTime > 4000) {
                                console.warn(
                                    '⚠️ LCP is too slow (>4s). Consider optimizing hero banner images.'
                                )
                            } else if (entry.startTime > 2500) {
                                console.info(
                                    'ℹ️ LCP could be improved (<2.5s for better score).'
                                )
                            } else {
                                console.log('✅ LCP is good!')
                            }
                        }
                    }
                })

                lcpObserver.observe({
                    entryTypes: ['largest-contentful-paint'],
                })

                // Monitor CSS loading performance
                const cssObserver = new PerformanceObserver((list) => {
                    for (const entry of list.getEntries()) {
                        if (
                            entry.name.includes('_nuxt') &&
                            entry.name.includes('.css')
                        ) {
                            console.log(
                                `📦 CSS loaded: ${entry.name} in ${entry.duration}ms`
                            )

                            if (entry.duration > 1000) {
                                console.warn(
                                    `⚠️ Slow CSS loading: ${entry.name} took ${entry.duration}ms`
                                )
                            }
                        }
                    }
                })

                cssObserver.observe({ entryTypes: ['resource'] })

                // Monitor long tasks without interfering
                const longTaskObserver = new PerformanceObserver((list) => {
                    for (const entry of list.getEntries()) {
                        if (entry.duration > 100) {
                            console.warn(
                                `⚠️ Long task detected: ${entry.duration}ms`
                            )
                        }
                    }
                })

                longTaskObserver.observe({ entryTypes: ['longtask'] })
            }
        }

        // Execute safe optimizations
        safeOptimizeJavaScript()
        safeOptimizeCSS()
        safePreloadResources()
        safeDNSPrefetch()
        safeMonitorPerformance()
    }

    // Run optimizations when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', safeOptimizePerformance)
    } else {
        safeOptimizePerformance()
    }

    // Re-run on route changes with delay to avoid conflicts
    const router = useRouter()
    router.afterEach(() => {
        setTimeout(safeOptimizePerformance, 200)
    })
})
