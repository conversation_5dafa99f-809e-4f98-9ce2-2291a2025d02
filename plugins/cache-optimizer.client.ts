export default defineNuxtPlugin(() => {
    if (import.meta.client) {
        // Cache optimizer - improve cache lifetimes for static assets
        const optimizeCacheLifetimes = () => {
            console.log('🗂️ Starting cache lifetime optimization...')

            // 1. Analyze current cache status
            const analyzeCacheStatus = () => {
                const assets = {
                    images: [],
                    scripts: [],
                    stylesheets: [],
                    fonts: []
                }

                // Analyze images
                const images = document.querySelectorAll('img[src]')
                images.forEach(img => {
                    const src = img.getAttribute('src')
                    if (src && src.startsWith('/assets/')) {
                        assets.images.push({
                            src,
                            element: img,
                            type: getAssetType(src),
                            size: getEstimatedSize(src)
                        })
                    }
                })

                // Analyze scripts
                const scripts = document.querySelectorAll('script[src]')
                scripts.forEach(script => {
                    const src = script.getAttribute('src')
                    if (src && (src.startsWith('/assets/') || src.startsWith('/_nuxt/'))) {
                        assets.scripts.push({
                            src,
                            element: script,
                            type: 'javascript',
                            size: getEstimatedSize(src)
                        })
                    }
                })

                // Analyze stylesheets
                const stylesheets = document.querySelectorAll('link[rel="stylesheet"]')
                stylesheets.forEach(link => {
                    const href = link.getAttribute('href')
                    if (href && (href.startsWith('/assets/') || href.startsWith('/_nuxt/'))) {
                        assets.stylesheets.push({
                            src: href,
                            element: link,
                            type: 'stylesheet',
                            size: getEstimatedSize(href)
                        })
                    }
                })

                console.log('📊 Cache Analysis Results:')
                console.log('  Images:', assets.images.length, 'files')
                console.log('  Scripts:', assets.scripts.length, 'files')
                console.log('  Stylesheets:', assets.stylesheets.length, 'files')

                return assets
            }

            // 2. Get asset type and priority
            const getAssetType = (src) => {
                if (src.includes('/hero-banner/')) return 'hero-image'
                if (src.includes('/home/')) return 'home-image'
                if (src.includes('.avif')) return 'avif-image'
                if (src.includes('.webp')) return 'webp-image'
                if (src.includes('.jpg') || src.includes('.jpeg')) return 'jpeg-image'
                if (src.includes('.png')) return 'png-image'
                return 'unknown-image'
            }

            // 3. Estimate file size based on path patterns
            const getEstimatedSize = (src) => {
                // Estimate based on known patterns from the performance report
                if (src.includes('danh-de-mien-phi/pc.png')) return 612 // KiB
                if (src.includes('club-world-cup/pc.jpg')) return 365
                if (src.includes('golden-star-warrios/pc.jpg')) return 219
                if (src.includes('jackpot/pc.jpg')) return 157
                if (src.includes('club-world-cup/mb.jpg')) return 144
                if (src.includes('nanoplayer.4.min.js')) return 118
                
                // Default estimates by file type
                if (src.includes('.png')) return 100
                if (src.includes('.jpg')) return 80
                if (src.includes('.webp')) return 60
                if (src.includes('.avif')) return 40
                if (src.includes('.js')) return 50
                if (src.includes('.css')) return 20
                
                return 10 // Default
            }

            // 4. Add preload hints for critical assets
            const addPreloadHints = (assets) => {
                // Preload large hero images
                const heroImages = assets.images.filter(img => 
                    img.type === 'hero-image' && img.size > 100
                )

                heroImages.slice(0, 2).forEach(img => {
                    const existing = document.querySelector(`link[href="${img.src}"][rel="preload"]`)
                    if (!existing) {
                        const link = document.createElement('link')
                        link.rel = 'preload'
                        link.as = 'image'
                        link.href = img.src
                        
                        // Add appropriate type
                        if (img.src.includes('.avif')) {
                            link.type = 'image/avif'
                        } else if (img.src.includes('.webp')) {
                            link.type = 'image/webp'
                        }
                        
                        // Add media query for responsive images
                        if (img.src.includes('/pc.')) {
                            link.media = '(min-width: 992px)'
                        } else if (img.src.includes('/mb.')) {
                            link.media = '(max-width: 991px)'
                        }
                        
                        link.setAttribute('fetchpriority', 'high')
                        document.head.appendChild(link)
                        console.log('🔗 Added preload for large asset:', img.src, `(~${img.size}KB)`)
                    }
                })

                // Preload critical JS files
                const criticalJS = assets.scripts.filter(script => 
                    script.src.includes('/entry.') || 
                    script.src.includes('/app.') ||
                    script.size > 100
                )

                criticalJS.forEach(script => {
                    const existing = document.querySelector(`link[href="${script.src}"][rel="modulepreload"]`)
                    if (!existing) {
                        const link = document.createElement('link')
                        link.rel = 'modulepreload'
                        link.href = script.src
                        link.setAttribute('fetchpriority', 'high')
                        document.head.appendChild(link)
                        console.log('🔗 Added modulepreload for:', script.src, `(~${script.size}KB)`)
                    }
                })
            }

            // 5. Optimize lazy loading for non-critical images
            const optimizeLazyLoading = (assets) => {
                const nonCriticalImages = assets.images.filter(img => 
                    img.type !== 'hero-image' && img.size > 50
                )

                nonCriticalImages.forEach(img => {
                    const element = img.element
                    
                    // Add lazy loading if not already present
                    if (!element.hasAttribute('loading')) {
                        element.setAttribute('loading', 'lazy')
                        console.log('⏰ Added lazy loading to:', img.src)
                    }
                    
                    // Add decoding hint
                    if (!element.hasAttribute('decoding')) {
                        element.setAttribute('decoding', 'async')
                    }
                })
            }

            // 6. Monitor cache performance
            const monitorCachePerformance = () => {
                if ('PerformanceObserver' in window) {
                    const observer = new PerformanceObserver((list) => {
                        list.getEntries().forEach(entry => {
                            if (entry.name.includes('/assets/') || entry.name.includes('/_nuxt/')) {
                                const transferSize = entry.transferSize || 0
                                const duration = entry.duration || 0
                                
                                // Log slow loading assets
                                if (duration > 500 && transferSize > 50000) {
                                    console.warn('🐌 Slow loading large asset:', {
                                        name: entry.name,
                                        duration: `${duration.toFixed(2)}ms`,
                                        size: `${Math.round(transferSize / 1024)}KB`,
                                        recommendation: 'Consider adding cache headers or preloading'
                                    })
                                }
                                
                                // Log assets loaded from cache
                                if (transferSize === 0 && duration < 50) {
                                    console.log('✅ Asset loaded from cache:', entry.name)
                                }
                            }
                        })
                    })
                    observer.observe({ entryTypes: ['resource'] })
                }
            }

            // 7. Generate cache optimization report
            const generateCacheReport = (assets) => {
                const totalSize = assets.images.reduce((sum, img) => sum + img.size, 0) +
                                assets.scripts.reduce((sum, script) => sum + script.size, 0) +
                                assets.stylesheets.reduce((sum, css) => sum + css.size, 0)

                const largeAssets = [
                    ...assets.images,
                    ...assets.scripts,
                    ...assets.stylesheets
                ].filter(asset => asset.size > 100).sort((a, b) => b.size - a.size)

                const report = {
                    timestamp: new Date().toISOString(),
                    summary: {
                        totalAssets: assets.images.length + assets.scripts.length + assets.stylesheets.length,
                        estimatedTotalSize: `${totalSize}KB`,
                        largeAssets: largeAssets.length,
                        potentialSavings: `${Math.round(totalSize * 0.8)}KB` // Assuming 80% cache hit rate
                    },
                    largeAssets: largeAssets.slice(0, 10), // Top 10 largest assets
                    recommendations: [
                        'Add long cache headers (1 year) for static assets',
                        'Preload critical hero banner images',
                        'Use lazy loading for below-the-fold images',
                        'Enable Service Worker for aggressive caching'
                    ]
                }

                console.log('📊 Cache Optimization Report:')
                console.table(report.summary)
                console.log('🔍 Largest assets:', report.largeAssets.map(a => `${a.src} (~${a.size}KB)`))

                // Store report for debugging
                window.__CACHE_REPORT__ = report

                return report
            }

            // Execute optimization
            const executeOptimization = () => {
                const assets = analyzeCacheStatus()
                addPreloadHints(assets)
                optimizeLazyLoading(assets)
                monitorCachePerformance()
                const report = generateCacheReport(assets)
                
                console.log('✅ Cache lifetime optimization completed')
                return report
            }

            return executeOptimization()
        }

        // Run optimization when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', optimizeCacheLifetimes)
        } else {
            optimizeCacheLifetimes()
        }

        // Re-run on route changes
        const router = useRouter()
        router.afterEach(() => {
            setTimeout(optimizeCacheLifetimes, 300)
        })

        console.log('🗂️ Cache optimizer plugin loaded')
    }
})
