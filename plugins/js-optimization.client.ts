export default defineNuxtPlugin(() => {
    if (import.meta.client) {
        // JavaScript loading optimization to reduce render blocking
        const optimizeJSLoading = () => {
            // 1. Defer non-critical JavaScript
            const deferNonCriticalJS = () => {
                const nonCriticalPatterns = [
                    'analytics',
                    'gtm',
                    'facebook',
                    'twitter',
                    'social',
                    'chat',
                    'advertisement'
                ]

                const scripts = document.querySelectorAll('script[src]')
                scripts.forEach(scriptElement => {
                    const script = scriptElement as HTMLScriptElement
                    const isNonCritical = nonCriticalPatterns.some(pattern => 
                        script.src.includes(pattern)
                    )

                    if (isNonCritical && !script.defer && !script.async) {
                        script.defer = true
                        console.log('🚀 Deferred non-critical script:', script.src)
                    }
                })
            }

            // 2. Lazy load third-party scripts
            const lazyLoadThirdPartyScripts = () => {
                const thirdPartyScripts = [
                    {
                        src: 'https://www.googletagmanager.com/gtag/js',
                        condition: () => window.scrollY > 100 || document.readyState === 'complete'
                    },
                    {
                        src: 'https://connect.facebook.net/en_US/fbevents.js',
                        condition: () => window.scrollY > 200
                    }
                ]

                const loadScript = (scriptConfig: any) => {
                    if (document.querySelector(`script[src*="${scriptConfig.src}"]`)) {
                        return // Already loaded
                    }

                    const script = document.createElement('script')
                    script.src = scriptConfig.src
                    script.async = true
                    script.defer = true
                    document.head.appendChild(script)
                    console.log('📦 Lazy loaded script:', scriptConfig.src)
                }

                const checkAndLoad = () => {
                    thirdPartyScripts.forEach(scriptConfig => {
                        if (scriptConfig.condition()) {
                            loadScript(scriptConfig)
                        }
                    })
                }

                // Check on scroll and interaction
                let scrollTimeout: NodeJS.Timeout
                window.addEventListener('scroll', () => {
                    clearTimeout(scrollTimeout)
                    scrollTimeout = setTimeout(checkAndLoad, 100)
                }, { passive: true })

                // Check on user interaction
                const interactionEvents = ['click', 'touchstart', 'keydown']
                const handleInteraction = () => {
                    checkAndLoad()
                    interactionEvents.forEach(event => {
                        document.removeEventListener(event, handleInteraction)
                    })
                }

                interactionEvents.forEach(event => {
                    document.addEventListener(event, handleInteraction, { 
                        once: true, 
                        passive: true 
                    })
                })

                // Fallback: load after 3 seconds
                setTimeout(checkAndLoad, 3000)
            }

            // 3. Optimize inline scripts
            const optimizeInlineScripts = () => {
                const inlineScripts = document.querySelectorAll('script:not([src])')
                inlineScripts.forEach(scriptElement => {
                    const script = scriptElement as HTMLScriptElement
                    const content = script.textContent || script.innerHTML

                    // Move non-critical inline scripts to load later
                    if (content.includes('analytics') || 
                        content.includes('tracking') || 
                        content.includes('advertisement')) {
                        
                        // Remove from current position
                        const newScript = document.createElement('script')
                        newScript.textContent = content
                        
                        // Add to load queue
                        setTimeout(() => {
                            document.head.appendChild(newScript)
                            console.log('⏰ Delayed inline script execution')
                        }, 1000)
                        
                        script.remove()
                    }
                })
            }

            // 4. Preload critical JavaScript modules
            const preloadCriticalJS = () => {
                const criticalModules = [
                    '/_nuxt/entry.js',
                    '/_nuxt/app.js'
                ]

                criticalModules.forEach(modulePath => {
                    const existing = document.querySelector(`link[href="${modulePath}"]`)
                    if (!existing) {
                        const link = document.createElement('link')
                        link.rel = 'modulepreload'
                        link.href = modulePath
                        document.head.appendChild(link)
                        console.log('🔗 Preloaded critical module:', modulePath)
                    }
                })
            }

            // 5. Code splitting optimization
            const optimizeCodeSplitting = () => {
                // Dynamic import for non-critical features
                const loadFeatureOnDemand = async (featureName: string, condition: () => boolean) => {
                    if (!condition()) return

                    try {
                        switch (featureName) {
                            case 'swiper':
                                if (document.querySelector('.swiper-container')) {
                                    await import('swiper/css')
                                    console.log('📱 Loaded Swiper on demand')
                                }
                                break
                            case 'charts':
                                if (document.querySelector('.chart-container')) {
                                    // Dynamically import chart library
                                    console.log('📊 Would load charts on demand')
                                }
                                break
                        }
                    } catch (error) {
                        console.error('Failed to load feature:', featureName, error)
                    }
                }

                // Load features based on viewport intersection
                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            const element = entry.target
                            if (element.classList.contains('swiper-container')) {
                                loadFeatureOnDemand('swiper', () => true)
                            }
                            if (element.classList.contains('chart-container')) {
                                loadFeatureOnDemand('charts', () => true)
                            }
                            observer.unobserve(element)
                        }
                    })
                })

                // Observe feature containers
                const featureContainers = document.querySelectorAll('.swiper-container, .chart-container')
                featureContainers.forEach(container => observer.observe(container))
            }

            // 6. Service Worker for caching JavaScript
            const optimizeJSCaching = () => {
                if ('serviceWorker' in navigator) {
                    // Register service worker for aggressive JS caching
                    navigator.serviceWorker.ready.then(registration => {
                        // Send message to SW to cache critical JS files
                        registration.active?.postMessage({
                            type: 'CACHE_JS_FILES',
                            files: [
                                '/_nuxt/entry.js',
                                '/_nuxt/app.js',
                                '/_nuxt/vendors.js'
                            ]
                        })
                    })
                }
            }

            // Execute optimizations
            deferNonCriticalJS()
            preloadCriticalJS()
            optimizeJSCaching()

            // Execute after DOM is ready
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => {
                    lazyLoadThirdPartyScripts()
                    optimizeInlineScripts()
                    setTimeout(optimizeCodeSplitting, 500)
                })
            } else {
                lazyLoadThirdPartyScripts()
                optimizeInlineScripts()
                setTimeout(optimizeCodeSplitting, 500)
            }
        }

        // 7. Performance monitoring for JavaScript
        const monitorJSPerformance = () => {
            if ('PerformanceObserver' in window) {
                // Monitor long tasks caused by JavaScript
                const longTaskObserver = new PerformanceObserver((list) => {
                    list.getEntries().forEach(entry => {
                        if (entry.duration > 50) {
                            console.warn('⚠️ Long JavaScript task detected:', {
                                duration: entry.duration,
                                startTime: entry.startTime,
                                name: entry.name
                            })
                        }
                    })
                })
                longTaskObserver.observe({ entryTypes: ['longtask'] })

                // Monitor script loading performance
                const resourceObserver = new PerformanceObserver((list) => {
                    list.getEntries().forEach(entry => {
                        if (entry.name.includes('.js') && entry.duration > 100) {
                            console.warn('🐌 Slow JavaScript loading:', {
                                name: entry.name,
                                duration: entry.duration,
                                size: (entry as any).transferSize
                            })
                        }
                    })
                })
                resourceObserver.observe({ entryTypes: ['resource'] })
            }
        }

        // Execute all optimizations
        optimizeJSLoading()
        monitorJSPerformance()

        console.log('🚀 JavaScript optimization plugin loaded')
    }
})
