export default defineNuxtPlugin(() => {
    if (import.meta.client) {
        // Critical Performance Optimizer - Focus on highest impact optimizations
        const criticalPerformanceOptimizer = () => {
            console.log('⚡ Starting critical performance optimization...')

            // 1. FONT OPTIMIZATION - Dynamic font detection and preloading
            const optimizeFonts = () => {
                console.log('🔤 Optimizing font loading dynamically...')

                // Dynamic font detection from existing stylesheets and DOM
                const detectFontFiles = () => {
                    const fontFiles = new Set()

                    // Method 1: Scan existing stylesheets for font URLs
                    const stylesheets = document.querySelectorAll(
                        'link[rel="stylesheet"], style'
                    )
                    stylesheets.forEach((sheet) => {
                        try {
                            let cssText = ''
                            if (sheet.tagName === 'LINK') {
                                // For external stylesheets, we'll check the href
                                const href = sheet.getAttribute('href')
                                if (href && href.includes('/_nuxt/')) {
                                    // This is likely a Nuxt-generated CSS file that might contain fonts
                                    console.log(
                                        '🔤 Found potential font-containing CSS:',
                                        href
                                    )
                                }
                            } else if (sheet.tagName === 'STYLE') {
                                cssText = sheet.textContent || ''
                            }

                            // Look for font-face declarations or font URLs
                            const fontUrlRegex =
                                /url\(['"]?([^'"]*\.woff2?)['"]*\)/gi
                            let match
                            while (
                                (match = fontUrlRegex.exec(cssText)) !== null
                            ) {
                                if (
                                    match[1].includes('Montserrat') ||
                                    match[1].includes('/_nuxt/')
                                ) {
                                    fontFiles.add(match[1])
                                }
                            }
                        } catch (e) {
                            // Ignore CORS errors for external stylesheets
                        }
                    })

                    // Method 2: Check for existing font preloads and extract pattern
                    const existingFontPreloads = document.querySelectorAll(
                        'link[rel="preload"][as="font"]'
                    )
                    existingFontPreloads.forEach((link) => {
                        const href = link.getAttribute('href')
                        if (
                            href &&
                            (href.includes('Montserrat') ||
                                href.includes('/_nuxt/'))
                        ) {
                            fontFiles.add(href)
                        }
                    })

                    // Method 3: Use performance API to detect loaded font resources
                    if (
                        'performance' in window &&
                        performance.getEntriesByType
                    ) {
                        const resourceEntries =
                            performance.getEntriesByType('resource')
                        resourceEntries.forEach((entry) => {
                            if (
                                entry.name.includes('.woff2') &&
                                (entry.name.includes('Montserrat') ||
                                    entry.name.includes('/_nuxt/'))
                            ) {
                                // Extract just the path part
                                const url = new URL(entry.name)
                                fontFiles.add(url.pathname)
                            }
                        })
                    }

                    // Method 4: Scan for common Nuxt font patterns in head
                    const allLinks = document.querySelectorAll(
                        'link[href*="/_nuxt/"]'
                    )
                    allLinks.forEach((link) => {
                        const href = link.getAttribute('href')
                        if (href && href.match(/Montserrat.*\.woff2$/)) {
                            fontFiles.add(href)
                        }
                    })

                    return Array.from(fontFiles)
                }

                // Get detected font files
                const detectedFonts = detectFontFiles()
                console.log('🔤 Detected font files:', detectedFonts)

                // If no fonts detected, try common patterns
                if (detectedFonts.length === 0) {
                    console.log(
                        '🔤 No fonts detected, scanning for Nuxt font patterns...'
                    )

                    // Scan all script tags for potential font references
                    const scripts = document.querySelectorAll('script')
                    scripts.forEach((script) => {
                        const content = script.textContent || script.innerHTML
                        const fontMatches = content.match(
                            /['"](\/[^'"]*Montserrat[^'"]*\.woff2?)['"]|['"](\/[^'"]*\/_nuxt\/[^'"]*\.woff2?)['"]|/g
                        )
                        if (fontMatches) {
                            fontMatches.forEach((match) => {
                                const cleanMatch = match.replace(/['"]/g, '')
                                if (
                                    cleanMatch.includes('Montserrat') ||
                                    cleanMatch.includes('/_nuxt/')
                                ) {
                                    detectedFonts.push(cleanMatch)
                                }
                            })
                        }
                    })
                }

                // Preload detected fonts
                const preloadDetectedFonts = (fontFiles) => {
                    fontFiles.forEach((fontPath) => {
                        // Skip if already preloaded
                        if (
                            document.querySelector(
                                `link[href="${fontPath}"][rel="preload"]`
                            )
                        ) {
                            return
                        }

                        const link = document.createElement('link')
                        link.rel = 'preload'
                        link.as = 'font'
                        link.type = 'font/woff2'
                        link.href = fontPath
                        link.crossOrigin = 'anonymous'
                        link.setAttribute('fetchpriority', 'high')
                        link.setAttribute('data-dynamic-font', 'true')

                        // Insert at the very beginning of head for highest priority
                        document.head.insertBefore(
                            link,
                            document.head.firstChild
                        )
                        console.log('🔤 Dynamically preloaded font:', fontPath)
                    })
                }

                // Execute font preloading
                if (detectedFonts.length > 0) {
                    preloadDetectedFonts(detectedFonts)
                } else {
                    console.log(
                        '🔤 No Montserrat fonts detected for preloading'
                    )
                }

                // Add font-display: swap to existing font faces
                const addFontDisplaySwap = () => {
                    const style = document.createElement('style')
                    style.textContent = `
                        @font-face {
                            font-family: 'Montserrat';
                            font-weight: 400;
                            font-style: normal;
                            font-display: swap;
                            src: url('/_nuxt/Montserrat-400-3.BcziCZ2I.woff2') format('woff2');
                        }
                        @font-face {
                            font-family: 'Montserrat';
                            font-weight: 400;
                            font-style: normal;
                            font-display: swap;
                            src: url('/_nuxt/Montserrat-400-4.C2XKUkC8.woff2') format('woff2');
                        }
                        @font-face {
                            font-family: 'Montserrat';
                            font-weight: 400;
                            font-style: normal;
                            font-display: swap;
                            src: url('/_nuxt/Montserrat-400-5.AeMhpAKq.woff2') format('woff2');
                        }
                        /* Fallback font to prevent layout shift */
                        body {
                            font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                        }
                    `
                    document.head.appendChild(style)
                    console.log(
                        '🔤 Added font-display: swap for all Montserrat fonts'
                    )
                }

                addFontDisplaySwap()
            }

            // 2. CRITICAL RESOURCE PRELOADING
            const preloadCriticalResources = () => {
                console.log('🚀 Preloading critical resources...')

                const { isMobile } = useDevice()
                const staticUrl = useRuntimeConfig().public.staticUrl

                // Critical resources based on current page
                const criticalResources = [
                    // Hero banner (LCP candidate)
                    {
                        href: `${staticUrl}/home/<USER>/jackpot/${
                            isMobile ? 'mb' : 'pc'
                        }.avif`,
                        as: 'image',
                        type: 'image/avif',
                        fetchpriority: 'high',
                        media: isMobile
                            ? '(max-width: 991px)'
                            : '(min-width: 992px)',
                    },
                    // Fallback hero banner
                    {
                        href: `${staticUrl}/home/<USER>/jackpot/${
                            isMobile ? 'mb' : 'pc'
                        }.webp`,
                        as: 'image',
                        type: 'image/webp',
                        fetchpriority: 'high',
                        media: isMobile
                            ? '(max-width: 991px)'
                            : '(min-width: 992px)',
                    },
                ]

                criticalResources.forEach((resource) => {
                    if (
                        !document.querySelector(`link[href="${resource.href}"]`)
                    ) {
                        const link = document.createElement('link')
                        link.rel = 'preload'
                        link.as = resource.as
                        link.href = resource.href
                        if (resource.type) link.type = resource.type
                        if (resource.media) link.media = resource.media
                        link.setAttribute(
                            'fetchpriority',
                            resource.fetchpriority
                        )

                        document.head.insertBefore(
                            link,
                            document.head.firstChild
                        )
                        console.log(
                            '🚀 Preloaded critical resource:',
                            resource.href
                        )
                    }
                })
            }

            // 3. ELIMINATE RENDER BLOCKING RESOURCES
            const eliminateRenderBlocking = () => {
                console.log('🚫 Eliminating render blocking resources...')

                // Make non-critical CSS non-blocking
                const stylesheets = document.querySelectorAll(
                    'link[rel="stylesheet"]'
                )
                stylesheets.forEach((link) => {
                    const href = link.getAttribute('href') || ''

                    // Keep critical CSS blocking, make others non-blocking
                    if (
                        !href.includes('entry') &&
                        !href.includes('app') &&
                        !href.includes('main')
                    ) {
                        // Make non-blocking
                        link.setAttribute('media', 'print')
                        link.setAttribute(
                            'onload',
                            "this.media='all'; this.onload=null;"
                        )
                        console.log('🚫 Made CSS non-blocking:', href)
                    }
                })

                // Defer non-critical JavaScript
                const scripts = document.querySelectorAll('script[src]')
                scripts.forEach((script) => {
                    const src = script.getAttribute('src') || ''

                    // Defer third-party and non-critical scripts
                    if (
                        src.includes('analytics') ||
                        src.includes('gtm') ||
                        src.includes('facebook') ||
                        src.includes('social') ||
                        src.includes('chat') ||
                        src.includes('advertisement')
                    ) {
                        if (
                            !script.hasAttribute('defer') &&
                            !script.hasAttribute('async')
                        ) {
                            script.setAttribute('defer', 'true')
                            script.setAttribute('fetchpriority', 'low')
                            console.log('🚫 Deferred non-critical script:', src)
                        }
                    }
                })
            }

            // 4. OPTIMIZE IMAGES FOR PERFORMANCE
            const optimizeImages = () => {
                console.log('🖼️ Optimizing images...')

                // Optimize hero banner images (LCP candidates)
                const heroImages = document.querySelectorAll(
                    'img[src*="hero-banner"]'
                )
                heroImages.forEach((imgElement, index) => {
                    const img = imgElement
                    if (index === 0) {
                        // First hero image - LCP candidate
                        img.setAttribute('loading', 'eager')
                        img.setAttribute('fetchpriority', 'high')
                        img.setAttribute('decoding', 'sync')
                        console.log(
                            '🖼️ Optimized LCP hero image:',
                            img.getAttribute('src')
                        )
                    } else {
                        // Other hero images
                        img.setAttribute('loading', 'lazy')
                        img.setAttribute('fetchpriority', 'low')
                        img.setAttribute('decoding', 'async')
                    }
                })

                // Optimize provider images (below the fold)
                const providerImages = document.querySelectorAll(
                    'img[src*="/providers/"]'
                )
                providerImages.forEach((imgElement) => {
                    const img = imgElement
                    img.setAttribute('loading', 'lazy')
                    img.setAttribute('fetchpriority', 'low')
                    img.setAttribute('decoding', 'async')
                    console.log(
                        '🖼️ Optimized provider image for lazy loading:',
                        img.getAttribute('src')
                    )
                })

                // Add intersection observer for lazy images
                if ('IntersectionObserver' in window) {
                    const lazyImageObserver = new IntersectionObserver(
                        (entries) => {
                            entries.forEach((entry) => {
                                if (entry.isIntersecting) {
                                    const img = entry.target
                                    const dataSrc = img.getAttribute('data-src')
                                    if (dataSrc) {
                                        img.setAttribute('src', dataSrc)
                                        img.removeAttribute('data-src')
                                        lazyImageObserver.unobserve(img)
                                    }
                                }
                            })
                        },
                        {
                            rootMargin: '50px 0px',
                            threshold: 0.01,
                        }
                    )

                    // Observe lazy images
                    document
                        .querySelectorAll('img[loading="lazy"]')
                        .forEach((img) => {
                            lazyImageObserver.observe(img)
                        })
                }
            }

            // 5. OPTIMIZE NETWORK CONNECTIONS
            const optimizeNetworkConnections = () => {
                console.log('🌐 Optimizing network connections...')

                // Preconnect to critical domains
                const criticalDomains = [
                    'https://fonts.googleapis.com',
                    'https://fonts.gstatic.com',
                ]

                criticalDomains.forEach((domain) => {
                    if (
                        !document.querySelector(
                            `link[rel="preconnect"][href="${domain}"]`
                        )
                    ) {
                        const link = document.createElement('link')
                        link.rel = 'preconnect'
                        link.href = domain
                        link.crossOrigin = 'anonymous'
                        document.head.insertBefore(
                            link,
                            document.head.firstChild
                        )
                        console.log('🌐 Added preconnect:', domain)
                    }
                })

                // DNS prefetch for less critical domains
                const prefetchDomains = [
                    '//www.googletagmanager.com',
                    '//connect.facebook.net',
                    '//www.google-analytics.com',
                ]

                prefetchDomains.forEach((domain) => {
                    if (
                        !document.querySelector(
                            `link[rel="dns-prefetch"][href="${domain}"]`
                        )
                    ) {
                        const link = document.createElement('link')
                        link.rel = 'dns-prefetch'
                        link.href = domain
                        document.head.appendChild(link)
                        console.log('🌐 Added DNS prefetch:', domain)
                    }
                })
            }

            // 6. CRITICAL CSS INLINING
            const inlineCriticalCSS = () => {
                console.log('🎨 Inlining critical CSS...')

                const criticalCSS = `
                    /* Critical CSS for above-the-fold content */
                    .hero-banner {
                        position: relative;
                        width: 100%;
                        aspect-ratio: 1920/1080;
                        contain: layout style paint;
                    }
                    
                    @media (max-width: 991px) {
                        .hero-banner {
                            aspect-ratio: 430/195;
                        }
                    }
                    
                    .hero-banner img {
                        width: 100%;
                        height: auto;
                        display: block;
                        object-fit: cover;
                        opacity: 1 !important;
                        visibility: visible !important;
                    }
                    
                    /* Prevent layout shift for providers */
                    .providers-container {
                        height: 3.375rem;
                    }
                    
                    @media (min-width: 1024px) {
                        .providers-container {
                            height: 6rem;
                        }
                    }
                    
                    /* Font loading optimization */
                    body {
                        font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                        font-display: swap;
                    }
                `

                const style = document.createElement('style')
                style.textContent = criticalCSS
                style.setAttribute('data-critical', 'true')
                document.head.insertBefore(style, document.head.firstChild)
                console.log('🎨 Inlined critical CSS')
            }

            // 7. PERFORMANCE MONITORING
            const monitorCriticalPerformance = () => {
                if ('PerformanceObserver' in window) {
                    // Monitor LCP
                    const lcpObserver = new PerformanceObserver((list) => {
                        list.getEntries().forEach((entry) => {
                            const lcp = entry.startTime
                            console.log(`⚡ LCP: ${lcp.toFixed(2)}ms`)

                            if (lcp < 2500) {
                                console.log('✅ LCP is excellent! (<2.5s)')
                            } else if (lcp < 4000) {
                                console.warn(
                                    '⚠️ LCP needs improvement (2.5s-4s)'
                                )
                            } else {
                                console.error('❌ LCP is poor (>4s)')
                            }
                        })
                    })
                    lcpObserver.observe({
                        entryTypes: ['largest-contentful-paint'],
                    })

                    // Monitor font loading
                    const fontObserver = new PerformanceObserver((list) => {
                        list.getEntries().forEach((entry) => {
                            if (
                                entry.name.includes('Montserrat') ||
                                entry.name.includes('.woff2')
                            ) {
                                const duration = entry.duration || 0
                                console.log(
                                    `🔤 Font loaded: ${
                                        entry.name
                                    } in ${duration.toFixed(2)}ms`
                                )

                                if (duration > 500) {
                                    console.warn(
                                        '⚠️ Slow font loading:',
                                        entry.name
                                    )
                                }
                            }
                        })
                    })
                    fontObserver.observe({ entryTypes: ['resource'] })
                }
            }

            // 8. GENERATE PERFORMANCE REPORT
            const generateCriticalReport = () => {
                const report = {
                    timestamp: new Date().toISOString(),
                    optimizations: {
                        fontsOptimized: true,
                        criticalResourcesPreloaded: true,
                        renderBlockingEliminated: true,
                        imagesOptimized: true,
                        networkOptimized: true,
                        criticalCSSInlined: true,
                        performanceMonitored: true,
                    },
                    metrics: {
                        preloadedFonts: document.querySelectorAll(
                            'link[rel="preload"][as="font"]'
                        ).length,
                        preloadedImages: document.querySelectorAll(
                            'link[rel="preload"][as="image"]'
                        ).length,
                        deferredScripts:
                            document.querySelectorAll('script[defer]').length,
                        lazyImages: document.querySelectorAll(
                            'img[loading="lazy"]'
                        ).length,
                    },
                    recommendations: [
                        'Monitor LCP scores regularly',
                        'Consider using a CDN for fonts',
                        'Optimize large images further',
                        'Remove unused CSS and JavaScript',
                    ],
                }

                console.log('📊 Critical Performance Optimization Report:')
                console.table(report.optimizations)
                console.table(report.metrics)

                window['__CRITICAL_PERFORMANCE_REPORT__'] = report
                return report
            }

            // EXECUTE ALL CRITICAL OPTIMIZATIONS
            const executeCriticalOptimizations = () => {
                // Execute in priority order
                inlineCriticalCSS() // Highest priority - prevent FOUC
                optimizeFonts() // Fix 995ms network dependency
                preloadCriticalResources() // LCP optimization
                eliminateRenderBlocking() // Remove blocking resources
                optimizeNetworkConnections() // Faster connections
                optimizeImages() // Image performance
                monitorCriticalPerformance() // Track improvements

                const report = generateCriticalReport()

                console.log('✅ Critical performance optimization completed')
                return report
            }

            return executeCriticalOptimizations()
        }

        // Execute immediately for maximum impact
        criticalPerformanceOptimizer()

        // Re-run on route changes
        const router = useRouter()
        router.afterEach(() => {
            setTimeout(criticalPerformanceOptimizer, 100)
        })

        console.log('⚡ Critical performance optimizer loaded')
    }
})
