export default defineNuxtPlugin(() => {
    if (import.meta.client) {
        // Lightweight Performance Optimizer - Minimal JS payload
        const optimize = () => {
            // 1. CRITICAL FONT PRELOADING (Ultra minimal)
            const f = new Set()

            // Only use Performance API - most efficient
            if (performance.getEntriesByType) {
                performance.getEntriesByType('resource').forEach((e) => {
                    if (
                        e.name.includes('.woff2') &&
                        e.name.includes('/_nuxt/')
                    ) {
                        try {
                            f.add(new URL(e.name).pathname)
                        } catch {}
                    }
                })
            }

            // Preload fonts (minimal DOM manipulation)
            f.forEach((p) => {
                if (!document.querySelector(`link[href="${p}"]`)) {
                    const l = document.createElement('link')
                    l.rel = 'preload'
                    l.as = 'font'
                    l.href = p
                    l.crossOrigin = 'anonymous'
                    document.head.prepend(l)
                }
            })

            // 2. LCP IMAGE PRELOADING (Device-specific)
            const { isMobile } = useDevice()
            const s = useRuntimeConfig().public.staticUrl
            const img = `${s}/home/<USER>/jackpot/${
                isMobile ? 'mb' : 'pc'
            }.avif`

            if (!document.querySelector(`link[href="${img}"]`)) {
                const l = document.createElement('link')
                l.rel = 'preload'
                l.as = 'image'
                l.href = img
                l.setAttribute('fetchpriority', 'high')
                document.head.prepend(l)
            }

            // 3. DEFER NON-CRITICAL SCRIPTS (Minimal check)
            document.querySelectorAll('script[src]').forEach((s) => {
                const src = s.getAttribute('src') || ''
                if (
                    src.includes('analytics') ||
                    src.includes('gtm') ||
                    src.includes('facebook')
                ) {
                    if (!s.hasAttribute('defer')) {
                        s.setAttribute('defer', 'true')
                        s.setAttribute('fetchpriority', 'low')
                    }
                }
            })

            // 4. LAZY LOAD PROVIDER IMAGES (Simple)
            document
                .querySelectorAll('img[src*="/providers/"]')
                .forEach((i) => {
                    i.setAttribute('loading', 'lazy')
                    i.setAttribute('fetchpriority', 'low')
                })

            // 5. MINIMAL PERFORMANCE MONITORING
            if ('PerformanceObserver' in window) {
                const o = new PerformanceObserver((list) => {
                    list.getEntries().forEach((e) => {
                        if (e.entryType === 'largest-contentful-paint') {
                            const lcp = e.startTime
                            if (lcp < 2500) {
                                console.log(
                                    `✅ LCP: ${lcp.toFixed(0)}ms (Good)`
                                )
                            } else {
                                console.warn(
                                    `⚠️ LCP: ${lcp.toFixed(
                                        0
                                    )}ms (Needs improvement)`
                                )
                            }
                        }
                    })
                })
                o.observe({ entryTypes: ['largest-contentful-paint'] })
            }

            return f.size
        }

        // Execute immediately (no DOM ready check to save bytes)
        const result = optimize()

        // Re-run on route changes (minimal)
        const router = useRouter()
        router.afterEach(() => setTimeout(optimize, 100))

        console.log(`⚡ Lightweight optimizer: ${result} fonts preloaded`)
    }
})
