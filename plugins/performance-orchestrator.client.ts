export default defineNuxtPlugin(() => {
    if (import.meta.client) {
        // Performance orchestrator - coordinates all optimization plugins
        const orchestratePerformanceOptimizations = () => {
            console.log('🎯 Starting performance orchestration...')

            // 1. Wait for resource detection to complete
            const waitForResourceDetection = () => {
                return new Promise((resolve) => {
                    if (window.__DETECTED_RESOURCES__) {
                        resolve(window.__DETECTED_RESOURCES__)
                    } else {
                        document.addEventListener(
                            'resourcesDetected',
                            (event) => {
                                resolve(event.detail)
                            },
                            { once: true }
                        )
                    }
                })
            }

            // 2. Apply optimizations based on detected resources
            const applyOptimizations = async () => {
                const resources = await waitForResourceDetection()
                console.log(
                    '📊 Applying optimizations based on detected resources:',
                    resources
                )

                // Apply CSS optimizations
                if (resources.css) {
                    applyCSSOptimizations(resources.css)
                }

                // Apply JS optimizations
                if (resources.js) {
                    applyJSOptimizations(resources.js)
                }

                // Apply font optimizations
                if (resources.fonts) {
                    applyFontOptimizations(resources.fonts)
                }

                // Apply image optimizations
                if (resources.images) {
                    applyImageOptimizations(resources.images)
                }

                console.log('✅ All optimizations applied')
            }

            // 3. CSS optimization implementation
            const applyCSSOptimizations = (cssResources) => {
                console.log('🎨 Applying CSS optimizations...')

                // Preload critical CSS
                cssResources.critical.forEach((href) => {
                    const existing = document.querySelector(
                        `link[href="${href}"][rel="preload"]`
                    )
                    if (!existing) {
                        const link = document.createElement('link')
                        link.rel = 'preload'
                        link.as = 'style'
                        link.href = href
                        link.setAttribute('fetchpriority', 'high')
                        document.head.appendChild(link)
                        console.log('🔗 Preloaded critical CSS:', href)
                    }
                })

                // Defer non-critical CSS
                cssResources.nonCritical.forEach((href) => {
                    const stylesheet = document.querySelector(
                        `link[href="${href}"][rel="stylesheet"]`
                    )
                    if (stylesheet) {
                        // Convert to preload
                        stylesheet.setAttribute('rel', 'preload')
                        stylesheet.setAttribute('as', 'style')
                        stylesheet.setAttribute(
                            'onload',
                            "this.onload=null;this.rel='stylesheet'"
                        )

                        // Add noscript fallback
                        const noscript = document.createElement('noscript')
                        const fallback = document.createElement('link')
                        fallback.setAttribute('rel', 'stylesheet')
                        fallback.setAttribute('href', href)
                        noscript.appendChild(fallback)

                        if (stylesheet.parentNode) {
                            stylesheet.parentNode.insertBefore(
                                noscript,
                                stylesheet.nextSibling
                            )
                        }

                        console.log('⏰ Deferred non-critical CSS:', href)
                    }
                })
            }

            // 4. JS optimization implementation
            const applyJSOptimizations = (jsResources) => {
                console.log('⚡ Applying JS optimizations...')

                // Preload critical JS modules
                jsResources.critical.forEach((src) => {
                    const existing = document.querySelector(
                        `link[href="${src}"][rel="modulepreload"]`
                    )
                    if (!existing) {
                        const link = document.createElement('link')
                        link.rel = 'modulepreload'
                        link.href = src
                        link.setAttribute('fetchpriority', 'high')
                        document.head.appendChild(link)
                        console.log('🔗 Preloaded critical JS:', src)
                    }
                })

                // Defer non-critical JS
                jsResources.nonCritical.forEach((src) => {
                    const script = document.querySelector(
                        `script[src="${src}"]`
                    )
                    if (script && !script.defer && !script.async) {
                        script.defer = true
                        console.log('⏰ Deferred non-critical JS:', src)
                    }
                })
            }

            // 5. Font optimization implementation
            const applyFontOptimizations = (fontResources) => {
                console.log('🔤 Applying font optimizations...')

                // Preload first 2 critical fonts
                fontResources.slice(0, 2).forEach((fontUrl) => {
                    const existing = document.querySelector(
                        `link[href="${fontUrl}"]`
                    )
                    if (!existing) {
                        const link = document.createElement('link')
                        link.rel = 'preload'
                        link.as = 'font'
                        link.type = 'font/woff2'
                        link.crossOrigin = 'anonymous'
                        link.href = fontUrl
                        document.head.appendChild(link)
                        console.log('🔤 Preloaded font:', fontUrl)
                    }
                })

                // Optimize Google Fonts CSS with font-display: swap
                const googleFontsLinks = document.querySelectorAll(
                    'link[href*="fonts.googleapis.com"]'
                )
                googleFontsLinks.forEach((link) => {
                    const href = link.getAttribute('href') || ''
                    if (!href.includes('display=swap')) {
                        try {
                            const url = new URL(href)
                            url.searchParams.set('display', 'swap')
                            link.setAttribute('href', url.toString())
                            console.log('🔤 Added font-display: swap to:', href)
                        } catch (e) {
                            console.warn('Failed to optimize font URL:', href)
                        }
                    }
                })
            }

            // 6. Image optimization implementation
            const applyImageOptimizations = (imageResources) => {
                console.log('🖼️ Applying image optimizations...')

                // Preload critical images (hero banners)
                imageResources.critical.slice(0, 2).forEach((src) => {
                    const existing = document.querySelector(
                        `link[href="${src}"][rel="preload"]`
                    )
                    if (!existing) {
                        const link = document.createElement('link')
                        link.rel = 'preload'
                        link.as = 'image'
                        link.href = src

                        // Set appropriate type based on file extension
                        if (src.includes('.avif')) {
                            link.type = 'image/avif'
                        } else if (src.includes('.webp')) {
                            link.type = 'image/webp'
                        }

                        // Add media query for responsive images
                        if (src.includes('/pc.')) {
                            link.media = '(min-width: 992px)'
                        } else if (src.includes('/mb.')) {
                            link.media = '(max-width: 991px)'
                        }

                        document.head.appendChild(link)
                        console.log('🖼️ Preloaded critical image:', src)
                    }
                })
            }

            // 7. Performance monitoring
            const monitorPerformance = () => {
                if ('PerformanceObserver' in window) {
                    const observer = new PerformanceObserver((list) => {
                        list.getEntries().forEach((entry) => {
                            if (
                                entry.entryType === 'largest-contentful-paint'
                            ) {
                                const lcp = entry.startTime
                                console.log(
                                    '🎯 LCP after orchestrated optimization:',
                                    lcp.toFixed(2),
                                    'ms'
                                )

                                if (lcp > 2500) {
                                    console.warn(
                                        '⚠️ LCP still needs improvement (target: <2.5s)'
                                    )
                                } else {
                                    console.log('✅ LCP is good! (<2.5s)')
                                }
                            }
                        })
                    })
                    observer.observe({
                        entryTypes: ['largest-contentful-paint'],
                    })
                }
            }

            // 8. Report optimization results
            const reportResults = async () => {
                const resources = await waitForResourceDetection()

                const report = {
                    timestamp: new Date().toISOString(),
                    optimizations: {
                        criticalCSS: resources.css?.critical?.length || 0,
                        deferredCSS: resources.css?.nonCritical?.length || 0,
                        criticalJS: resources.js?.critical?.length || 0,
                        deferredJS: resources.js?.nonCritical?.length || 0,
                        preloadedFonts: Math.min(
                            resources.fonts?.length || 0,
                            2
                        ),
                        preloadedImages: Math.min(
                            resources.images?.critical?.length || 0,
                            2
                        ),
                    },
                    recommendations: resources.recommendations || [],
                }

                console.log('📊 Performance Optimization Report:')
                console.table(report.optimizations)

                // Store report for debugging
                window.__PERFORMANCE_REPORT__ = report

                return report
            }

            // Execute orchestration
            const executeOrchestration = async () => {
                try {
                    await applyOptimizations()
                    monitorPerformance()
                    const report = await reportResults()

                    console.log(
                        '🎉 Performance orchestration completed successfully!'
                    )
                    return report
                } catch (error) {
                    console.error('❌ Performance orchestration failed:', error)
                    throw error
                }
            }

            return executeOrchestration()
        }

        // Start orchestration after a short delay to ensure other plugins are loaded
        setTimeout(() => {
            orchestratePerformanceOptimizations()
        }, 100)

        // Re-run on route changes
        const router = useRouter()
        router.afterEach(() => {
            setTimeout(orchestratePerformanceOptimizations, 200)
        })

        console.log('🎭 Performance orchestrator plugin loaded')
    }
})
