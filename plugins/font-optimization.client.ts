export default defineNuxtPlugin(() => {
    if (import.meta.client) {
        // Font optimization plugin using dynamic detection
        const fontOptimizationPlugin = () => {
            console.log('🔤 Starting font optimization plugin...')
            
            // Use the dynamic font optimization composable
            const { optimizeFonts } = useDynamicFontOptimization()
            
            // Run font optimization
            const runOptimization = () => {
                try {
                    const results = optimizeFonts()
                    
                    if (results) {
                        console.log('🔤 Font optimization completed:', {
                            fontFiles: results.fontFiles.length,
                            fontFamilies: results.fontFamilies.length
                        })
                        
                        // Store results for debugging
                        window['__FONT_OPTIMIZATION_RESULTS__'] = results
                    }
                } catch (error) {
                    console.error('🔤 Font optimization failed:', error)
                }
            }
            
            // Additional font loading optimizations
            const additionalOptimizations = () => {
                // 1. Monitor font loading performance
                if ('PerformanceObserver' in window) {
                    const fontObserver = new PerformanceObserver((list) => {
                        list.getEntries().forEach(entry => {
                            if (entry.name.includes('.woff2') || entry.name.includes('.woff')) {
                                const duration = entry.duration || 0
                                const transferSize = entry.transferSize || 0
                                
                                console.log('🔤 Font loaded:', {
                                    name: entry.name.split('/').pop(),
                                    duration: `${duration.toFixed(2)}ms`,
                                    size: `${Math.round(transferSize / 1024)}KB`
                                })
                                
                                if (duration > 500) {
                                    console.warn('🔤 Slow font loading detected:', entry.name)
                                }
                            }
                        })
                    })
                    
                    fontObserver.observe({ entryTypes: ['resource'] })
                }
                
                // 2. Add font loading event listeners
                document.fonts?.addEventListener('loadingdone', () => {
                    console.log('🔤 All fonts loaded successfully')
                    document.body.classList.add('fonts-loaded')
                })
                
                document.fonts?.addEventListener('loadingerror', (event) => {
                    console.warn('🔤 Font loading error:', event)
                })
                
                // 3. Preload fonts that might be discovered later
                const preloadAdditionalFonts = () => {
                    // Check for any CSS files that might contain font references
                    const stylesheets = document.querySelectorAll('link[rel="stylesheet"][href*="/_nuxt/"]')
                    
                    stylesheets.forEach(async (stylesheet) => {
                        try {
                            const href = stylesheet.getAttribute('href')
                            if (href) {
                                // Try to fetch and scan the CSS for font URLs
                                const response = await fetch(href)
                                const cssText = await response.text()
                                
                                // Look for font URLs in the CSS
                                const fontUrlRegex = /url\(['"]?([^'"]*\.woff2?)['"]*\)/gi
                                let match
                                
                                while ((match = fontUrlRegex.exec(cssText)) !== null) {
                                    const fontUrl = match[1]
                                    
                                    // Check if it's not already preloaded
                                    if (!document.querySelector(`link[href="${fontUrl}"][rel="preload"]`)) {
                                        const link = document.createElement('link')
                                        link.rel = 'preload'
                                        link.as = 'font'
                                        link.type = 'font/woff2'
                                        link.href = fontUrl
                                        link.crossOrigin = 'anonymous'
                                        link.setAttribute('fetchpriority', 'high')
                                        link.setAttribute('data-discovered-font', 'true')
                                        
                                        document.head.appendChild(link)
                                        console.log('🔤 Discovered and preloaded font from CSS:', fontUrl)
                                    }
                                }
                            }
                        } catch (error) {
                            // Ignore CORS or network errors
                            console.log('🔤 Could not scan CSS for fonts:', error.message)
                        }
                    })
                }
                
                // Run additional font discovery after a delay
                setTimeout(preloadAdditionalFonts, 1000)
            }
            
            // Execute optimizations
            runOptimization()
            additionalOptimizations()
        }
        
        // Run immediately if DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', fontOptimizationPlugin)
        } else {
            fontOptimizationPlugin()
        }
        
        // Also run on route changes to catch any new fonts
        const router = useRouter()
        router.afterEach(() => {
            setTimeout(fontOptimizationPlugin, 100)
        })
        
        console.log('🔤 Font optimization plugin loaded')
    }
})
