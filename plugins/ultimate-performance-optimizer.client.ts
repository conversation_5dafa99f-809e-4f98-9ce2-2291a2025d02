export default defineNuxtPlugin(() => {
    if (import.meta.client) {
        // Ultimate Performance Optimizer - All optimizations in one file
        const ultimatePerformanceOptimizer = () => {
            console.log('⚡ Starting ultimate performance optimization...')

            // 1. DYNAMIC FONT OPTIMIZATION - Fix 995ms Network Dependency Tree
            const optimizeFonts = () => {
                console.log('🔤 Optimizing fonts dynamically...')

                const detectFonts = () => {
                    const fontFiles = new Set()

                    // Method 1: Performance API
                    if (performance.getEntriesByType) {
                        performance
                            .getEntriesByType('resource')
                            .forEach((entry) => {
                                if (
                                    entry.name.includes('.woff2') &&
                                    (entry.name.includes('/_nuxt/') ||
                                        entry.name.includes('Montserrat'))
                                ) {
                                    try {
                                        const url = new URL(entry.name)
                                        fontFiles.add(url.pathname)
                                    } catch (e) {}
                                }
                            })
                    }

                    // Method 2: Existing preloads
                    document
                        .querySelectorAll('link[rel="preload"][as="font"]')
                        .forEach((link) => {
                            const href = link.getAttribute('href')
                            if (
                                href &&
                                (href.includes('/_nuxt/') ||
                                    href.includes('Montserrat'))
                            ) {
                                fontFiles.add(href)
                            }
                        })

                    // Method 3: Scan stylesheets
                    document
                        .querySelectorAll(
                            'link[rel="stylesheet"][href*="/_nuxt/"]'
                        )
                        .forEach((link) => {
                            const href = link.getAttribute('href')
                            if (href) {
                                fetch(href)
                                    .then((response) => response.text())
                                    .then((cssText) => {
                                        const fontUrlRegex =
                                            /url\(['"]?([^'"]*\.woff2?)['"]*\)/gi
                                        let match
                                        while (
                                            (match =
                                                fontUrlRegex.exec(cssText)) !==
                                            null
                                        ) {
                                            if (
                                                match[1].includes('/_nuxt/') ||
                                                match[1].includes('Montserrat')
                                            ) {
                                                fontFiles.add(match[1])
                                                preloadFont(match[1])
                                            }
                                        }
                                    })
                                    .catch(() => {})
                            }
                        })

                    return Array.from(fontFiles)
                }

                const preloadFont = (fontPath) => {
                    if (
                        document.querySelector(
                            `link[href="${fontPath}"][rel="preload"]`
                        )
                    )
                        return

                    const link = document.createElement('link')
                    link.rel = 'preload'
                    link.as = 'font'
                    link.type = 'font/woff2'
                    link.href = fontPath
                    link.crossOrigin = 'anonymous'
                    link.setAttribute('fetchpriority', 'high')

                    document.head.insertBefore(link, document.head.firstChild)
                    console.log('🔤 Preloaded font:', fontPath)
                }

                // Execute font detection and preloading
                const detectedFonts = detectFonts()
                detectedFonts.forEach(preloadFont)

                // Add font-display: swap
                const style = document.createElement('style')
                style.textContent = `
                    @font-face {
                        font-family: 'Montserrat';
                        font-display: swap;
                    }
                    body {
                        font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                        font-display: swap;
                    }
                `
                document.head.appendChild(style)

                return detectedFonts
            }

            // 2. LCP OPTIMIZATION
            const optimizeLCP = () => {
                console.log('🎯 Optimizing LCP...')

                const { isMobile } = useDevice()
                const staticUrl = useRuntimeConfig().public.staticUrl

                // Preload hero images
                const heroImage = `${staticUrl}/home/<USER>/jackpot/${
                    isMobile ? 'mb' : 'pc'
                }.avif`
                const mediaQuery = isMobile
                    ? '(max-width: 991px)'
                    : '(min-width: 992px)'

                if (!document.querySelector(`link[href="${heroImage}"]`)) {
                    const link = document.createElement('link')
                    link.rel = 'preload'
                    link.as = 'image'
                    link.type = 'image/avif'
                    link.href = heroImage
                    link.media = mediaQuery
                    link.setAttribute('fetchpriority', 'high')
                    document.head.insertBefore(link, document.head.firstChild)
                    console.log('🎯 Preloaded LCP image:', heroImage)
                }

                // Optimize hero images
                document
                    .querySelectorAll('img[src*="hero-banner"]')
                    .forEach((img, index) => {
                        if (index === 0) {
                            img.setAttribute('loading', 'eager')
                            img.setAttribute('fetchpriority', 'high')
                            img.setAttribute('decoding', 'sync')
                        }
                    })
            }

            // 3. ELIMINATE RENDER BLOCKING
            const eliminateRenderBlocking = () => {
                console.log('🚫 Eliminating render blocking...')

                // Make non-critical CSS non-blocking
                document
                    .querySelectorAll('link[rel="stylesheet"]')
                    .forEach((link) => {
                        const href = link.getAttribute('href') || ''
                        if (
                            !href.includes('entry') &&
                            !href.includes('app') &&
                            !href.includes('main')
                        ) {
                            link.setAttribute('media', 'print')
                            link.setAttribute('onload', "this.media='all'")
                            console.log('🚫 Made CSS non-blocking:', href)
                        }
                    })

                // Defer non-critical scripts
                document.querySelectorAll('script[src]').forEach((script) => {
                    const src = script.getAttribute('src') || ''
                    if (
                        src.includes('analytics') ||
                        src.includes('gtm') ||
                        src.includes('facebook') ||
                        src.includes('social') ||
                        src.includes('chat')
                    ) {
                        if (!script.hasAttribute('defer')) {
                            script.setAttribute('defer', 'true')
                            script.setAttribute('fetchpriority', 'low')
                            console.log('🚫 Deferred script:', src)
                        }
                    }
                })
            }

            // 4. OPTIMIZE IMAGES
            const optimizeImages = () => {
                console.log('🖼️ Optimizing images...')

                // Lazy load provider images
                document
                    .querySelectorAll('img[src*="/providers/"]')
                    .forEach((img) => {
                        img.setAttribute('loading', 'lazy')
                        img.setAttribute('fetchpriority', 'low')
                        img.setAttribute('decoding', 'async')
                    })

                // Add intersection observer for better lazy loading
                if ('IntersectionObserver' in window) {
                    const imageObserver = new IntersectionObserver(
                        (entries) => {
                            entries.forEach((entry) => {
                                if (entry.isIntersecting) {
                                    const img = entry.target
                                    const dataSrc = img.getAttribute('data-src')
                                    if (dataSrc) {
                                        img.setAttribute('src', dataSrc)
                                        img.removeAttribute('data-src')
                                        imageObserver.unobserve(img)
                                    }
                                }
                            })
                        },
                        { rootMargin: '50px 0px' }
                    )

                    document
                        .querySelectorAll('img[loading="lazy"]')
                        .forEach((img) => {
                            imageObserver.observe(img)
                        })
                }
            }

            // 5. NETWORK OPTIMIZATION
            const optimizeNetwork = () => {
                console.log('🌐 Optimizing network...')

                // Preconnect to critical domains
                const criticalDomains = [
                    'https://fonts.googleapis.com',
                    'https://fonts.gstatic.com',
                ]

                criticalDomains.forEach((domain) => {
                    if (
                        !document.querySelector(
                            `link[rel="preconnect"][href="${domain}"]`
                        )
                    ) {
                        const link = document.createElement('link')
                        link.rel = 'preconnect'
                        link.href = domain
                        link.crossOrigin = 'anonymous'
                        document.head.appendChild(link)
                        console.log('🌐 Added preconnect:', domain)
                    }
                })

                // DNS prefetch for less critical domains
                const prefetchDomains = [
                    '//www.googletagmanager.com',
                    '//connect.facebook.net',
                ]

                prefetchDomains.forEach((domain) => {
                    if (
                        !document.querySelector(
                            `link[rel="dns-prefetch"][href="${domain}"]`
                        )
                    ) {
                        const link = document.createElement('link')
                        link.rel = 'dns-prefetch'
                        link.href = domain
                        document.head.appendChild(link)
                        console.log('🌐 Added DNS prefetch:', domain)
                    }
                })
            }

            // 6. PERFORMANCE MONITORING
            const monitorPerformance = () => {
                if ('PerformanceObserver' in window) {
                    // Monitor LCP
                    const lcpObserver = new PerformanceObserver((list) => {
                        list.getEntries().forEach((entry) => {
                            const lcp = entry.startTime
                            console.log(`⚡ LCP: ${lcp.toFixed(2)}ms`)

                            if (lcp < 2500) {
                                console.log('✅ LCP is excellent! (<2.5s)')
                            } else {
                                console.warn('⚠️ LCP needs improvement (>2.5s)')
                            }
                        })
                    })
                    lcpObserver.observe({
                        entryTypes: ['largest-contentful-paint'],
                    })

                    // Monitor font loading
                    const resourceObserver = new PerformanceObserver((list) => {
                        list.getEntries().forEach((entry) => {
                            if (
                                entry.name.includes('.woff2') ||
                                entry.name.includes('.woff')
                            ) {
                                const duration = entry.duration || 0
                                console.log(
                                    `🔤 Font loaded: ${entry.name
                                        .split('/')
                                        .pop()} in ${duration.toFixed(2)}ms`
                                )
                            }
                        })
                    })
                    resourceObserver.observe({ entryTypes: ['resource'] })
                }
            }

            // 7. GENERATE REPORT
            const generateReport = (detectedFonts) => {
                const report = {
                    timestamp: new Date().toISOString(),
                    optimizations: {
                        fontsOptimized: detectedFonts.length,
                        lcpOptimized: true,
                        renderBlockingEliminated: true,
                        imagesOptimized: true,
                        networkOptimized: true,
                        performanceMonitored: true,
                    },
                    detectedFonts,
                    recommendations: [
                        'Monitor LCP scores regularly',
                        'Check font loading performance',
                        'Optimize large images further',
                    ],
                }

                console.log('📊 Ultimate Performance Report:')
                console.table(report.optimizations)

                window['__ULTIMATE_PERFORMANCE_REPORT__'] = report
                return report
            }

            // EXECUTE ALL OPTIMIZATIONS
            const executeOptimizations = () => {
                const detectedFonts = optimizeFonts() // Fix 995ms network dependency
                optimizeLCP() // Optimize LCP images
                eliminateRenderBlocking() // Remove blocking resources
                optimizeImages() // Lazy load images
                optimizeNetwork() // Optimize connections
                monitorPerformance() // Track performance

                const report = generateReport(detectedFonts)

                console.log('✅ Ultimate performance optimization completed')
                return report
            }

            return executeOptimizations()
        }

        // Execute optimization
        if (document.readyState === 'loading') {
            document.addEventListener(
                'DOMContentLoaded',
                ultimatePerformanceOptimizer
            )
        } else {
            ultimatePerformanceOptimizer()
        }

        // Re-run on route changes
        const router = useRouter()
        router.afterEach(() => {
            setTimeout(ultimatePerformanceOptimizer, 200)
        })

        console.log('⚡ Ultimate performance optimizer loaded')
    }
})
