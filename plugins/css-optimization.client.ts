export default defineNuxtPlugin(() => {
    if (import.meta.client) {
        // Advanced CSS loading optimization to reduce render blocking
        const optimizeCSSLoading = () => {
            // 1. Critical CSS inline injection
            const injectCriticalCSS = () => {
                const criticalCSS = `
                    /* Critical above-the-fold styles */
                    body { margin: 0; padding: 0; font-family: 'Montserrat', sans-serif; }
                    .header { position: fixed; top: 0; left: 0; right: 0; z-index: 1000; background: #fff; }
                    .hero-banner { 
                        position: relative; 
                        width: 100%; 
                        min-height: 400px; 
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    }
                    .hero-banner img { 
                        width: 100%; 
                        height: auto; 
                        display: block;
                        object-fit: cover;
                    }
                    .loading-overlay {
                        position: fixed;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        background: rgba(255, 255, 255, 0.9);
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        z-index: 9999;
                    }
                    .spinner {
                        width: 40px;
                        height: 40px;
                        border: 4px solid #f3f3f3;
                        border-top: 4px solid #3498db;
                        border-radius: 50%;
                        animation: spin 1s linear infinite;
                    }
                    @keyframes spin {
                        0% { transform: rotate(0deg); }
                        100% { transform: rotate(360deg); }
                    }
                    /* Hide non-critical elements initially */
                    .swiper-container,
                    .footer,
                    .game-section,
                    .promotion-section { 
                        opacity: 0; 
                        transition: opacity 0.3s ease-in-out; 
                    }
                    /* Mobile optimizations */
                    @media (max-width: 768px) {
                        .hero-banner { min-height: 300px; }
                        .header { padding: 10px; }
                    }
                `

                const style = document.createElement('style')
                style.id = 'critical-css'
                style.textContent = criticalCSS
                document.head.insertBefore(style, document.head.firstChild)
            }

            // 2. Defer non-critical CSS files
            const deferNonCriticalCSS = () => {
                const nonCriticalPatterns = [
                    'swiper-vue',
                    'nuxt-icon',
                    'button.B9v6vxHo',
                    'login-form',
                    'default.D7SrQHRa'
                ]

                // Find and defer non-critical stylesheets
                const allLinks = document.querySelectorAll('link[rel="stylesheet"]')
                allLinks.forEach(linkElement => {
                    const link = linkElement as any
                    const isNonCritical = nonCriticalPatterns.some(pattern => 
                        link.href.includes(pattern)
                    )

                    if (isNonCritical) {
                        // Convert to preload and load asynchronously
                        link.rel = 'preload'
                        link.as = 'style'
                        link.onload = function() {
                            this.onload = null
                            this.rel = 'stylesheet'
                        }
                        // Fallback for browsers that don't support preload
                        const noscript = document.createElement('noscript')
                        const fallbackLink = link.cloneNode(true) as any
                        fallbackLink.rel = 'stylesheet'
                        noscript.appendChild(fallbackLink)
                        link.parentNode?.insertBefore(noscript, link.nextSibling)
                    }
                })
            }

            // 3. Progressive enhancement for below-the-fold content
            const progressiveEnhancement = () => {
                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            const element = entry.target as HTMLElement
                            element.style.opacity = '1'
                            observer.unobserve(element)
                        }
                    })
                }, {
                    rootMargin: '50px 0px',
                    threshold: 0.1
                })

                // Observe below-the-fold elements
                const belowFoldSelectors = [
                    '.swiper-container',
                    '.footer',
                    '.game-section',
                    '.promotion-section'
                ]

                belowFoldSelectors.forEach(selector => {
                    const elements = document.querySelectorAll(selector)
                    elements.forEach(element => {
                        observer.observe(element)
                    })
                })
            }

            // 4. Optimize font loading with font-display: swap
            const optimizeFontLoading = () => {
                // Add font-display: swap to Google Fonts
                const fontLinks = document.querySelectorAll('link[href*="fonts.googleapis.com"]')
                fontLinks.forEach(linkElement => {
                    const link = linkElement as any
                    if (!link.href.includes('display=swap')) {
                        const url = new URL(link.href)
                        url.searchParams.set('display', 'swap')
                        link.href = url.toString()
                    }
                })

                // Preload critical font files
                const criticalFonts = [
                    'https://fonts.gstatic.com/s/montserrat/v25/JTUSjIg1_i6t8kCHKm459Wlhyw.woff2'
                ]

                criticalFonts.forEach(fontUrl => {
                    const link = document.createElement('link')
                    link.rel = 'preload'
                    link.as = 'font'
                    link.type = 'font/woff2'
                    link.crossOrigin = 'anonymous'
                    link.href = fontUrl
                    document.head.appendChild(link)
                })
            }

            // 5. Remove unused CSS (basic implementation)
            const removeUnusedCSS = () => {
                // This is a simplified version - in production, use tools like PurgeCSS
                setTimeout(() => {
                    const unusedSelectors = [
                        '.unused-class',
                        '.debug-info',
                        '.development-only'
                    ]

                    const styleSheets = Array.from(document.styleSheets)
                    styleSheets.forEach(sheet => {
                        try {
                            const rules = Array.from(sheet.cssRules || sheet.rules || [])
                            rules.forEach((rule, index) => {
                                const ruleText = rule.cssText || ''
                                if (unusedSelectors.some(selector => ruleText.includes(selector))) {
                                    sheet.deleteRule(index)
                                }
                            })
                        } catch (e) {
                            // Cross-origin stylesheets can't be accessed
                            console.log('Cannot access stylesheet:', sheet.href)
                        }
                    })
                }, 2000)
            }

            // Execute optimizations in order
            injectCriticalCSS()
            deferNonCriticalCSS()
            optimizeFontLoading()
            
            // Progressive enhancement after DOM is ready
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => {
                    setTimeout(progressiveEnhancement, 100)
                    removeUnusedCSS()
                })
            } else {
                setTimeout(progressiveEnhancement, 100)
                removeUnusedCSS()
            }
        }

        // 6. Resource hints optimization
        const optimizeResourceHints = () => {
            // Add dns-prefetch for external domains
            const externalDomains = [
                'https://fonts.googleapis.com',
                'https://fonts.gstatic.com',
                'https://www.googletagmanager.com',
                'https://www.google-analytics.com'
            ]

            externalDomains.forEach(domain => {
                const existing = document.querySelector(`link[href="${domain}"]`)
                if (!existing) {
                    const link = document.createElement('link')
                    link.rel = 'dns-prefetch'
                    link.href = domain
                    document.head.appendChild(link)
                }
            })

            // Preconnect to critical origins
            const criticalOrigins = [
                'https://fonts.googleapis.com',
                'https://fonts.gstatic.com'
            ]

            criticalOrigins.forEach(origin => {
                const existing = document.querySelector(`link[href="${origin}"][rel="preconnect"]`)
                if (!existing) {
                    const link = document.createElement('link')
                    link.rel = 'preconnect'
                    link.href = origin
                    link.crossOrigin = 'anonymous'
                    document.head.appendChild(link)
                }
            })
        }

        // Execute optimizations immediately
        optimizeCSSLoading()
        optimizeResourceHints()

        // Monitor performance
        if ('PerformanceObserver' in window) {
            const observer = new PerformanceObserver((list) => {
                list.getEntries().forEach(entry => {
                    if (entry.entryType === 'largest-contentful-paint') {
                        console.log('🎯 LCP after CSS optimization:', entry.startTime, 'ms')
                        if (entry.startTime > 2500) {
                            console.warn('⚠️ LCP still needs improvement. Consider further optimizations.')
                        }
                    }
                })
            })
            observer.observe({ entryTypes: ['largest-contentful-paint'] })
        }
    }
})
