export default defineNuxtPlugin(() => {
    if (import.meta.client) {
        // Advanced resource hints optimization
        const optimizeResourceHints = () => {
            // 1. Dynamic preload for critical resources based on user behavior
            const dynamicPreload = () => {
                const criticalResources = [
                    {
                        href: '/_nuxt/entry.css',
                        as: 'style',
                        priority: 'high',
                        condition: () => true // Always preload
                    },
                    {
                        href: '/_nuxt/index.C42_iUI5.css',
                        as: 'style',
                        priority: 'high',
                        condition: () => true // Always preload
                    },
                    {
                        href: '/assets/images/home/<USER>/jackpot/pc.avif',
                        as: 'image',
                        type: 'image/avif',
                        media: '(min-width: 992px)',
                        priority: 'high',
                        condition: () => window.innerWidth >= 992
                    },
                    {
                        href: '/assets/images/home/<USER>/jackpot/mb.avif',
                        as: 'image',
                        type: 'image/avif',
                        media: '(max-width: 991px)',
                        priority: 'high',
                        condition: () => window.innerWidth < 992
                    }
                ]

                criticalResources.forEach(resource => {
                    if (resource.condition()) {
                        const existing = document.querySelector(`link[href="${resource.href}"]`)
                        if (!existing) {
                            const link = document.createElement('link')
                            link.rel = 'preload'
                            link.href = resource.href
                            link.as = resource.as
                            
                            if (resource.type) link.type = resource.type
                            if (resource.media) link.media = resource.media
                            if (resource.priority) link.setAttribute('fetchpriority', resource.priority)
                            
                            document.head.appendChild(link)
                            console.log('🔗 Dynamically preloaded:', resource.href)
                        }
                    }
                })
            }

            // 2. Intelligent DNS prefetch based on user interaction
            const intelligentDNSPrefetch = () => {
                const externalDomains = [
                    'https://fonts.googleapis.com',
                    'https://fonts.gstatic.com',
                    'https://www.googletagmanager.com',
                    'https://www.google-analytics.com',
                    'https://connect.facebook.net'
                ]

                // Prefetch immediately for critical domains
                const criticalDomains = [
                    'https://fonts.googleapis.com',
                    'https://fonts.gstatic.com'
                ]

                criticalDomains.forEach(domain => {
                    const existing = document.querySelector(`link[href="${domain}"][rel="dns-prefetch"]`)
                    if (!existing) {
                        const link = document.createElement('link')
                        link.rel = 'dns-prefetch'
                        link.href = domain
                        document.head.appendChild(link)
                        console.log('🌐 DNS prefetched:', domain)
                    }
                })

                // Prefetch other domains on user interaction
                const prefetchOnInteraction = () => {
                    externalDomains.forEach(domain => {
                        if (!criticalDomains.includes(domain)) {
                            const existing = document.querySelector(`link[href="${domain}"][rel="dns-prefetch"]`)
                            if (!existing) {
                                const link = document.createElement('link')
                                link.rel = 'dns-prefetch'
                                link.href = domain
                                document.head.appendChild(link)
                                console.log('🌐 DNS prefetched on interaction:', domain)
                            }
                        }
                    })
                }

                // Trigger on first user interaction
                const interactionEvents = ['click', 'touchstart', 'scroll', 'keydown']
                const handleFirstInteraction = () => {
                    prefetchOnInteraction()
                    interactionEvents.forEach(event => {
                        document.removeEventListener(event, handleFirstInteraction)
                    })
                }

                interactionEvents.forEach(event => {
                    document.addEventListener(event, handleFirstInteraction, { 
                        once: true, 
                        passive: true 
                    })
                })
            }

            // 3. Preconnect optimization with timing
            const optimizePreconnect = () => {
                const preconnectDomains = [
                    {
                        href: 'https://fonts.googleapis.com',
                        crossorigin: false,
                        timing: 0 // Immediate
                    },
                    {
                        href: 'https://fonts.gstatic.com',
                        crossorigin: true,
                        timing: 0 // Immediate
                    },
                    {
                        href: 'https://www.googletagmanager.com',
                        crossorigin: false,
                        timing: 1000 // After 1 second
                    }
                ]

                preconnectDomains.forEach(domain => {
                    setTimeout(() => {
                        const existing = document.querySelector(`link[href="${domain.href}"][rel="preconnect"]`)
                        if (!existing) {
                            const link = document.createElement('link')
                            link.rel = 'preconnect'
                            link.href = domain.href
                            if (domain.crossorigin) {
                                link.crossOrigin = 'anonymous'
                            }
                            document.head.appendChild(link)
                            console.log('🔌 Preconnected:', domain.href)
                        }
                    }, domain.timing)
                })
            }

            // 4. Module preload for critical JavaScript
            const preloadCriticalModules = () => {
                const criticalModules = [
                    '/_nuxt/entry.js',
                    '/_nuxt/app.js'
                ]

                criticalModules.forEach(modulePath => {
                    const existing = document.querySelector(`link[href="${modulePath}"][rel="modulepreload"]`)
                    if (!existing) {
                        const link = document.createElement('link')
                        link.rel = 'modulepreload'
                        link.href = modulePath
                        link.setAttribute('fetchpriority', 'high')
                        document.head.appendChild(link)
                        console.log('📦 Module preloaded:', modulePath)
                    }
                })
            }

            // 5. Adaptive preloading based on connection quality
            const adaptivePreloading = () => {
                const connection = (navigator as any).connection || 
                                 (navigator as any).mozConnection || 
                                 (navigator as any).webkitConnection

                if (connection) {
                    const isSlowConnection = connection.effectiveType === 'slow-2g' || 
                                           connection.effectiveType === '2g' ||
                                           connection.saveData

                    if (isSlowConnection) {
                        console.log('🐌 Slow connection detected, reducing preloads')
                        // Only preload absolutely critical resources
                        const criticalOnly = [
                            '/_nuxt/entry.css',
                            '/assets/images/home/<USER>/jackpot/mb.avif'
                        ]

                        criticalOnly.forEach(href => {
                            const existing = document.querySelector(`link[href="${href}"]`)
                            if (!existing) {
                                const link = document.createElement('link')
                                link.rel = 'preload'
                                link.href = href
                                link.as = href.endsWith('.css') ? 'style' : 'image'
                                if (href.includes('.avif')) link.type = 'image/avif'
                                document.head.appendChild(link)
                            }
                        })
                    } else {
                        console.log('🚀 Fast connection detected, full preloading')
                        dynamicPreload()
                    }
                } else {
                    // Fallback: assume good connection
                    dynamicPreload()
                }
            }

            // 6. Intersection Observer for lazy preloading
            const lazyPreloadResources = () => {
                const lazyResources = [
                    {
                        selector: '.swiper-container',
                        resources: ['/_nuxt/swiper-vue.D2zhFq2K.css']
                    },
                    {
                        selector: '.game-section',
                        resources: ['/_nuxt/index.AJ5clg8T.css']
                    },
                    {
                        selector: '.footer',
                        resources: ['/_nuxt/default.D7SrQHRa.css']
                    }
                ]

                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            const element = entry.target
                            const resourceConfig = lazyResources.find(config => 
                                element.matches(config.selector)
                            )

                            if (resourceConfig) {
                                resourceConfig.resources.forEach(href => {
                                    const existing = document.querySelector(`link[href="${href}"]`)
                                    if (!existing) {
                                        const link = document.createElement('link')
                                        link.rel = 'preload'
                                        link.href = href
                                        link.as = 'style'
                                        document.head.appendChild(link)
                                        console.log('👁️ Lazy preloaded:', href)
                                    }
                                })
                                observer.unobserve(element)
                            }
                        }
                    })
                }, {
                    rootMargin: '100px 0px',
                    threshold: 0.1
                })

                // Observe elements when they exist
                const observeWhenReady = () => {
                    lazyResources.forEach(config => {
                        const elements = document.querySelectorAll(config.selector)
                        elements.forEach(element => observer.observe(element))
                    })
                }

                // Try to observe immediately, then retry after DOM changes
                observeWhenReady()
                setTimeout(observeWhenReady, 1000)
                setTimeout(observeWhenReady, 3000)
            }

            // Execute optimizations
            optimizePreconnect()
            preloadCriticalModules()
            intelligentDNSPrefetch()
            adaptivePreloading()
            lazyPreloadResources()

            console.log('🎯 Resource hints optimization loaded')
        }

        // Execute immediately
        optimizeResourceHints()

        // Re-run on route changes
        const router = useRouter()
        router.afterEach(() => {
            setTimeout(optimizeResourceHints, 100)
        })
    }
})
