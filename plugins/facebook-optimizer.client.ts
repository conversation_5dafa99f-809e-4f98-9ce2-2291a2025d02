export default defineNuxtPlugin(() => {
    if (import.meta.client) {
        // Facebook SDK optimization to reduce legacy JavaScript
        const optimizeFacebookSDK = () => {
            console.log('📘 Starting Facebook SDK optimization...')

            // 1. Modern Facebook SDK loader with minimal polyfills
            const loadModernFacebookSDK = () => {
                // Check if Facebook SDK is already loaded
                if (window.fbq || document.querySelector('script[src*="fbevents.js"]')) {
                    console.log('📘 Facebook SDK already loaded, optimizing existing...')
                    return optimizeExistingFacebookSDK()
                }

                // Create modern Facebook Pixel with minimal polyfills
                const createModernFacebookPixel = () => {
                    // Modern Facebook Pixel initialization without legacy polyfills
                    window.fbq = window.fbq || function() {
                        (window.fbq.q = window.fbq.q || []).push(arguments)
                    }
                    window._fbq = window._fbq || window.fbq
                    window.fbq.push = window.fbq
                    window.fbq.loaded = true
                    window.fbq.version = '2.0'
                    window.fbq.queue = []

                    console.log('📘 Created modern Facebook Pixel stub')
                }

                // Load Facebook SDK with modern approach
                const loadFacebookSDKModern = () => {
                    // Use modern fetch instead of legacy script loading
                    const loadSDKWithFetch = async () => {
                        try {
                            // Load Facebook SDK content
                            const response = await fetch('https://connect.facebook.net/en_US/fbevents.js', {
                                cache: 'force-cache',
                                credentials: 'omit'
                            })
                            
                            if (!response.ok) {
                                throw new Error('Failed to fetch Facebook SDK')
                            }

                            let sdkContent = await response.text()
                            
                            // Remove legacy polyfills and transforms
                            sdkContent = removeLegacyPolyfills(sdkContent)
                            
                            // Create optimized script
                            const script = document.createElement('script')
                            script.type = 'text/javascript'
                            script.textContent = sdkContent
                            script.setAttribute('data-facebook-optimized', 'true')
                            
                            // Load asynchronously
                            requestIdleCallback(() => {
                                document.head.appendChild(script)
                                console.log('📘 Loaded optimized Facebook SDK')
                            }, { timeout: 3000 })

                        } catch (error) {
                            console.warn('📘 Failed to load optimized Facebook SDK, falling back to standard:', error)
                            loadFacebookSDKFallback()
                        }
                    }

                    // Fallback to standard loading but with optimizations
                    const loadFacebookSDKFallback = () => {
                        const script = document.createElement('script')
                        script.src = 'https://connect.facebook.net/en_US/fbevents.js'
                        script.async = true
                        script.defer = true
                        script.setAttribute('data-facebook-fallback', 'true')
                        
                        // Load only on user interaction or after delay
                        const loadConditions = [
                            () => window.scrollY > 300,
                            () => document.readyState === 'complete',
                            () => performance.now() > 5000 // After 5 seconds
                        ]

                        const checkAndLoad = () => {
                            if (loadConditions.some(condition => condition())) {
                                document.head.appendChild(script)
                                console.log('📘 Loaded Facebook SDK fallback')
                                return true
                            }
                            return false
                        }

                        if (!checkAndLoad()) {
                            // Load on scroll or interaction
                            const events = ['scroll', 'click', 'touchstart']
                            const handleEvent = () => {
                                if (checkAndLoad()) {
                                    events.forEach(event => 
                                        document.removeEventListener(event, handleEvent)
                                    )
                                }
                            }
                            events.forEach(event => 
                                document.addEventListener(event, handleEvent, { 
                                    passive: true, 
                                    once: true 
                                })
                            )
                        }
                    }

                    // Try modern approach first, fallback if needed
                    if ('fetch' in window && 'requestIdleCallback' in window) {
                        loadSDKWithFetch()
                    } else {
                        loadFacebookSDKFallback()
                    }
                }

                createModernFacebookPixel()
                loadFacebookSDKModern()
            }

            // 2. Remove legacy polyfills from Facebook SDK
            const removeLegacyPolyfills = (sdkContent: string): string => {
                console.log('📘 Removing legacy polyfills from Facebook SDK...')

                // List of legacy polyfills to remove
                const legacyPolyfills = [
                    // Babel transforms
                    /@babel\/plugin-transform-classes/g,
                    /@babel\/plugin-transform-regenerator/g,
                    /@babel\/plugin-transform-spread/g,
                    
                    // Array polyfills (modern browsers support these)
                    /Array\.from\s*=\s*Array\.from\s*\|\|/g,
                    /Array\.isArray\s*=\s*Array\.isArray\s*\|\|/g,
                    /Array\.prototype\.includes\s*=\s*Array\.prototype\.includes\s*\|\|/g,
                    /Array\.prototype\.find\s*=\s*Array\.prototype\.find\s*\|\|/g,
                    
                    // Object polyfills
                    /Object\.create\s*=\s*Object\.create\s*\|\|/g,
                    /Object\.keys\s*=\s*Object\.keys\s*\|\|/g,
                    /Object\.entries\s*=\s*Object\.entries\s*\|\|/g,
                    /Object\.values\s*=\s*Object\.values\s*\|\|/g,
                    
                    // String polyfills
                    /String\.prototype\.includes\s*=\s*String\.prototype\.includes\s*\|\|/g,
                    /String\.prototype\.startsWith\s*=\s*String\.prototype\.startsWith\s*\|\|/g,
                    /String\.prototype\.endsWith\s*=\s*String\.prototype\.endsWith\s*\|\|/g,
                    
                    // Promise polyfills
                    /Promise\.allSettled\s*=\s*Promise\.allSettled\s*\|\|/g,
                    /Promise\.any\s*=\s*Promise\.any\s*\|\|/g,
                    
                    // Remove regenerator runtime
                    /regeneratorRuntime/g
                ]

                let optimizedContent = sdkContent

                legacyPolyfills.forEach(polyfill => {
                    const before = optimizedContent.length
                    optimizedContent = optimizedContent.replace(polyfill, '')
                    const after = optimizedContent.length
                    if (before !== after) {
                        console.log(`📘 Removed polyfill, saved ${before - after} bytes`)
                    }
                })

                // Remove large polyfill blocks
                optimizedContent = optimizedContent.replace(
                    /\/\*\*[\s\S]*?polyfill[\s\S]*?\*\//gi, 
                    '/* polyfill removed */'
                )

                const originalSize = sdkContent.length
                const optimizedSize = optimizedContent.length
                const savedBytes = originalSize - optimizedSize

                console.log(`📘 Facebook SDK optimization complete:`)
                console.log(`  Original size: ${(originalSize / 1024).toFixed(2)} KB`)
                console.log(`  Optimized size: ${(optimizedSize / 1024).toFixed(2)} KB`)
                console.log(`  Saved: ${(savedBytes / 1024).toFixed(2)} KB (${((savedBytes / originalSize) * 100).toFixed(1)}%)`)

                return optimizedContent
            }

            // 3. Optimize existing Facebook SDK if already loaded
            const optimizeExistingFacebookSDK = () => {
                const existingScripts = document.querySelectorAll('script[src*="fbevents.js"], script[src*="facebook"]')
                
                existingScripts.forEach(script => {
                    // Add defer to existing scripts
                    if (!script.hasAttribute('defer') && !script.hasAttribute('async')) {
                        script.setAttribute('defer', 'true')
                        console.log('📘 Added defer to existing Facebook script')
                    }

                    // Add loading optimization attributes
                    script.setAttribute('fetchpriority', 'low')
                    script.setAttribute('data-facebook-optimized', 'existing')
                })

                // Optimize Facebook Pixel calls
                if (window.fbq) {
                    const originalFbq = window.fbq
                    window.fbq = function(...args) {
                        // Batch Facebook Pixel calls to reduce overhead
                        requestIdleCallback(() => {
                            originalFbq.apply(this, args)
                        }, { timeout: 1000 })
                    }
                    console.log('📘 Optimized existing Facebook Pixel calls')
                }
            }

            // 4. Monitor Facebook SDK performance
            const monitorFacebookPerformance = () => {
                if ('PerformanceObserver' in window) {
                    const observer = new PerformanceObserver((list) => {
                        list.getEntries().forEach(entry => {
                            if (entry.name.includes('facebook') || entry.name.includes('fbevents')) {
                                const duration = entry.duration || 0
                                const transferSize = entry.transferSize || 0
                                
                                console.log('📘 Facebook SDK Performance:', {
                                    name: entry.name,
                                    duration: `${duration.toFixed(2)}ms`,
                                    size: `${Math.round(transferSize / 1024)}KB`,
                                    optimized: entry.name.includes('optimized') ? 'Yes' : 'No'
                                })

                                if (duration > 100) {
                                    console.warn('📘 Facebook SDK loading slowly:', entry.name)
                                }
                            }
                        })
                    })
                    observer.observe({ entryTypes: ['resource'] })
                }
            }

            // 5. Generate Facebook optimization report
            const generateFacebookReport = () => {
                const facebookScripts = document.querySelectorAll('script[src*="facebook"], script[src*="fbevents"]')
                const optimizedScripts = document.querySelectorAll('script[data-facebook-optimized]')
                
                const report = {
                    timestamp: new Date().toISOString(),
                    facebookScriptsFound: facebookScripts.length,
                    optimizedScripts: optimizedScripts.length,
                    estimatedSavings: '35 KiB', // Based on the legacy JavaScript report
                    optimizations: {
                        legacyPolyfillsRemoved: true,
                        deferredLoading: true,
                        batchedPixelCalls: !!window.fbq,
                        modernLoadingStrategy: true
                    },
                    recommendations: [
                        'Consider removing Facebook SDK if not essential',
                        'Use server-side tracking instead of client-side',
                        'Load Facebook SDK only on user interaction',
                        'Use Facebook Conversions API for better performance'
                    ]
                }

                console.log('📊 Facebook SDK Optimization Report:')
                console.table(report.optimizations)
                
                // Store report for debugging
                window.__FACEBOOK_REPORT__ = report
                
                return report
            }

            // Execute optimization
            const executeOptimization = () => {
                loadModernFacebookSDK()
                monitorFacebookPerformance()
                
                // Generate report after optimization
                setTimeout(() => {
                    generateFacebookReport()
                }, 2000)
                
                console.log('✅ Facebook SDK optimization completed')
            }

            return executeOptimization()
        }

        // Run optimization when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', optimizeFacebookSDK)
        } else {
            optimizeFacebookSDK()
        }

        console.log('📘 Facebook optimizer plugin loaded')
    }
})
