export default defineNuxtPlugin(() => {
    // Safe UI optimization - do not modify DOM
    const safeOptimizeUI = () => {
        // 1. Safe CSS optimization - only defer external CSS
        const safeOptimizeCSS = () => {
            // Only defer external stylesheets that are not critical
            const externalStyleSheets = document.querySelectorAll(
                'link[rel="stylesheet"][href*="googleapis.com"]'
            )

            externalStyleSheets.forEach((sheet) => {
                if (!sheet.hasAttribute('media')) {
                    sheet.setAttribute('media', 'print')
                    sheet.setAttribute('onload', "this.media='all'")
                }
            })
        }

        // 2. Safe font optimization - only add font-display: swap
        const safeOptimizeFonts = () => {
            // Add font-display: swap for fonts
            const style = document.createElement('style')
            style.textContent = `
                @font-face {
                    font-family: 'Montserrat';
                    font-display: swap;
                }
                @font-face {
                    font-family: 'Montserrat fallback';
                    font-display: swap;
                }
                @font-face {
                    font-family: 'Open Sans';
                    font-display: swap;
                }
            `
            document.head.appendChild(style)
        }

        // 3. Safe resource preloading - only preload critical images
        const safePreloadResources = () => {
            // Only preload hero banner images
            const criticalImages = [
                '/assets/images/home/<USER>/jackpot/pc.avif',
                '/assets/images/home/<USER>/jackpot/pc.webp',
                '/assets/images/home/<USER>/jackpot/pc.jpg',
                '/assets/images/home/<USER>/jackpot/mb.avif',
                '/assets/images/home/<USER>/jackpot/mb.webp',
                '/assets/images/home/<USER>/jackpot/mb.jpg',
            ]

            criticalImages.forEach((src) => {
                // Check if preload already exists
                const existingLink = document.querySelector(
                    `link[href="${src}"]`
                )
                if (!existingLink) {
                    const link = document.createElement('link')
                    link.rel = 'preload'
                    link.as = 'image'
                    link.href = src
                    link.type = src.includes('.avif')
                        ? 'image/avif'
                        : 'image/webp'
                    document.head.appendChild(link)
                }
            })
        }

        // 4. Safe performance monitoring - only log, do not modify
        const safeMonitorPerformance = () => {
            if ('PerformanceObserver' in window) {
                // Monitor LCP
                const lcpObserver = new PerformanceObserver((list) => {
                    for (const entry of list.getEntries()) {
                        if (entry.entryType === 'largest-contentful-paint') {
                            console.log('🎯 LCP:', entry.startTime, 'ms')

                            if (entry.startTime <= 2500) {
                                console.log('✅ LCP is good!')
                            } else if (entry.startTime <= 4000) {
                                console.log('⚠️ LCP needs improvement')
                            } else {
                                console.log('❌ LCP is poor')
                            }
                        }
                    }
                })

                lcpObserver.observe({
                    entryTypes: ['largest-contentful-paint'],
                })

                // Monitor slow resources
                const resourceObserver = new PerformanceObserver((list) => {
                    for (const entry of list.getEntries()) {
                        if (entry.entryType === 'resource') {
                            const resourceEntry = entry
                            if (resourceEntry.duration > 1000) {
                                console.warn(
                                    `🐌 Slow resource: ${resourceEntry.name} (${resourceEntry.duration}ms)`
                                )
                            }
                        }
                    }
                })

                resourceObserver.observe({ entryTypes: ['resource'] })
            }
        }

        // 5. Safe UI stability check
        const checkUIStability = () => {
            // Check if hero banner is loaded
            const heroBanner = document.querySelector('.hero-banner')
            if (heroBanner) {
                const img = heroBanner.querySelector('img')
                if (img && img.complete && img.naturalHeight !== 0) {
                    console.log('✅ Hero banner loaded successfully')
                } else {
                    console.log('⚠️ Hero banner still loading')
                }
            } else {
                console.log('❌ Hero banner not found')
            }

            // Check layout stability
            const body = document.body
            if (body.offsetWidth > 0 && body.offsetHeight > 0) {
                console.log('✅ Layout is stable')
            } else {
                console.log('❌ Layout is unstable')
            }
        }

        // Execute safe optimizations
        safeOptimizeCSS()
        safeOptimizeFonts()
        safePreloadResources()
        safeMonitorPerformance()

        // Check UI stability after a delay
        setTimeout(checkUIStability, 2000)
    }

    // Run optimizations when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', safeOptimizeUI)
    } else {
        safeOptimizeUI()
    }

    // Re-run on route changes with delay
    const router = useRouter()
    router.afterEach(() => {
        setTimeout(safeOptimizeUI, 300)
    })
})
