export default defineNuxtPlugin(() => {
    if (import.meta.client && 'serviceWorker' in navigator) {
        // Register service worker
        const registerSW = async () => {
            try {
                const registration = await navigator.serviceWorker.register(
                    '/sw.js',
                    {
                        scope: '/',
                    }
                )

                console.log(
                    '[SW] Service Worker registered successfully:',
                    registration
                )

                // Check for update
                registration.addEventListener('updatefound', () => {
                    const newWorker = registration.installing
                    if (newWorker) {
                        newWorker.addEventListener('statechange', () => {
                            if (
                                newWorker.state === 'installed' &&
                                navigator.serviceWorker.controller
                            ) {
                                // New version available, notify user
                                console.log('[SW] New version available')
                                // Show notification to user to reload page
                                showUpdateNotification()
                            }
                        })
                    }
                })

                // Handle when service worker is ready
                if (registration.active) {
                    console.log('[SW] Service Worker is active')
                }

                // Handle when new service worker is activated
                navigator.serviceWorker.addEventListener(
                    'controllerchange',
                    () => {
                        console.log('[SW] New service worker activated')
                        // Reload page to use new service worker
                        // window.location.reload()
                    }
                )

                // Handle message from service worker
                navigator.serviceWorker.addEventListener('message', (event) => {
                    console.log('[SW] Message from service worker:', event.data)
                })
            } catch (error) {
                console.error('[SW] Service Worker registration failed:', error)
            }
        }

        // Show notification when new version is available
        const showUpdateNotification = () => {
            // Create a notification component to notify user
            // or use browser notification API
            // if (
            //     'Notification' in window &&
            //     Notification.permission === 'granted'
            // ) {
            //     new Notification('Z01', {
            //         body: 'Có phiên bản mới! Nhấn để cập nhật.',
            //         icon: '/assets/favicon/favicon.ico',
            //         requireInteraction: true,
            //     })
            // }
        }

        // Register service worker when page is loaded
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', registerSW)
        } else {
            registerSW()
        }

        // Handle offline/online events
        window.addEventListener('online', () => {
            console.log('[SW] App is online')
            // Show notification or sync data
        })

        window.addEventListener('offline', () => {
            console.log('[SW] App is offline')
            // Show offline indicator
        })

        // Request notification permission
        const requestNotificationPermission = async () => {
            // if (
            //     'Notification' in window &&
            //     Notification.permission === 'default'
            // ) {
            //     const permission = await Notification.requestPermission()
            //     console.log('[SW] Notification permission:', permission)
            // }
        }

        // Request permission when user interact with page
        document.addEventListener(
            'click',
            () => {
                requestNotificationPermission()
            },
            { once: true }
        )
    }
})
