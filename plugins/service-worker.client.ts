export default defineNuxtPlugin(() => {
    if (import.meta.client && 'serviceWorker' in navigator) {
        // Advanced CSS optimization for render blocking
        const optimizeRenderBlockingCSS = () => {
            // 1. Defer non-critical CSS files
            const deferNonCriticalCSS = () => {
                const nonCriticalSelectors = [
                    'link[href*="swiper-vue"]',
                    'link[href*="nuxt-icon"]',
                    'link[href*="button.B9v6vxHo"]',
                    'link[href*="login-form"]',
                    'link[href*="default.D7SrQHRa"]'
                ]

                nonCriticalSelectors.forEach(selector => {
                    const links = document.querySelectorAll(selector)
                    links.forEach(linkElement => {
                        const link = linkElement as any
                        if (link.rel === 'stylesheet') {
                            // Convert to preload and load asynchronously
                            link.rel = 'preload'
                            link.as = 'style'
                            link.onload = function() {
                                this.onload = null
                                this.rel = 'stylesheet'
                            }
                        }
                    })
                })
            }

            // 2. Inline critical CSS for above-the-fold content
            const inlineCriticalCSS = () => {
                const criticalCSS = `
                    /* Critical CSS for hero banner and initial viewport */
                    .hero-banner { display: block; position: relative; }
                    .hero-banner img { width: 100%; height: auto; }
                    .header { position: fixed; top: 0; z-index: 1000; }
                    .loading-spinner { display: flex; justify-content: center; align-items: center; }
                    /* Hide non-critical elements initially */
                    .swiper-container { opacity: 0; transition: opacity 0.3s; }
                    .footer { display: none; }
                `

                const style = document.createElement('style')
                style.textContent = criticalCSS
                document.head.insertBefore(style, document.head.firstChild)
            }

            // 3. Lazy load CSS for below-the-fold content
            const lazyLoadBelowFoldCSS = () => {
                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            // Show swiper and other below-fold elements
                            const swiperContainers = document.querySelectorAll('.swiper-container')
                            swiperContainers.forEach(containerElement => {
                                const container = containerElement as HTMLElement
                                container.style.opacity = '1'
                            })

                            // Show footer
                            const footerElement = document.querySelector('.footer')
                            if (footerElement) {
                                const footer = footerElement as HTMLElement
                                footer.style.display = 'block'
                            }

                            observer.disconnect()
                        }
                    })
                })

                // Observe the first below-fold element
                const belowFoldElement = document.querySelector('.swiper-container, .footer, .game-section')
                if (belowFoldElement) {
                    observer.observe(belowFoldElement)
                }
            }

            // 4. Optimize font loading
            const optimizeFontLoading = () => {
                // Add font-display: swap to existing font links
                const fontLinks = document.querySelectorAll('link[href*="fonts.googleapis.com"]')
                fontLinks.forEach(linkElement => {
                    const link = linkElement as any
                    if (link.href.includes('display=swap')) return

                    const url = new URL(link.href)
                    url.searchParams.set('display', 'swap')
                    link.href = url.toString()
                })
            }

            // Execute optimizations
            deferNonCriticalCSS()
            inlineCriticalCSS()
            optimizeFontLoading()

            // Lazy load below-fold content after DOM is ready
            setTimeout(lazyLoadBelowFoldCSS, 100)
        }

        // Register service worker
        const registerSW = async () => {
            try {
                const registration = await navigator.serviceWorker.register(
                    '/sw.js',
                    {
                        scope: '/',
                    }
                )

                console.log(
                    '[SW] Service Worker registered successfully:',
                    registration
                )

                // Check for update
                registration.addEventListener('updatefound', () => {
                    const newWorker = registration.installing
                    if (newWorker) {
                        newWorker.addEventListener('statechange', () => {
                            if (
                                newWorker.state === 'installed' &&
                                navigator.serviceWorker.controller
                            ) {
                                // New version available, notify user
                                console.log('[SW] New version available')
                                // Show notification to user to reload page
                                showUpdateNotification()
                            }
                        })
                    }
                })

                // Handle when service worker is ready
                if (registration.active) {
                    console.log('[SW] Service Worker is active')
                }

                // Handle when new service worker is activated
                navigator.serviceWorker.addEventListener(
                    'controllerchange',
                    () => {
                        console.log('[SW] New service worker activated')
                        // Reload page to use new service worker
                        // window.location.reload()
                    }
                )

                // Handle message from service worker
                navigator.serviceWorker.addEventListener('message', (event) => {
                    console.log('[SW] Message from service worker:', event.data)
                })
            } catch (error) {
                console.error('[SW] Service Worker registration failed:', error)
            }
        }

        // Show notification when new version is available
        const showUpdateNotification = () => {
            // Create a notification component to notify user
            // or use browser notification API
            // if (
            //     'Notification' in window &&
            //     Notification.permission === 'granted'
            // ) {
            //     new Notification('Z01', {
            //         body: 'Có phiên bản mới! Nhấn để cập nhật.',
            //         icon: '/assets/favicon/favicon.ico',
            //         requireInteraction: true,
            //     })
            // }
        }

        // Optimize render blocking CSS immediately
        optimizeRenderBlockingCSS()

        // Register service worker when page is loaded
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', registerSW)
        } else {
            registerSW()
        }

        // Handle offline/online events
        window.addEventListener('online', () => {
            console.log('[SW] App is online')
            // Show notification or sync data
        })

        window.addEventListener('offline', () => {
            console.log('[SW] App is offline')
            // Show offline indicator
        })

        // Request notification permission
        const requestNotificationPermission = async () => {
            // if (
            //     'Notification' in window &&
            //     Notification.permission === 'default'
            // ) {
            //     const permission = await Notification.requestPermission()
            //     console.log('[SW] Notification permission:', permission)
            // }
        }

        // Request permission when user interact with page
        document.addEventListener(
            'click',
            () => {
                requestNotificationPermission()
            },
            { once: true }
        )
    }
})
