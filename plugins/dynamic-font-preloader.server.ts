import { readdir } from 'fs/promises'
import { join } from 'path'

export default defineNuxtPlugin(async (nuxtApp) => {
    // Dynamic font preloader - automatically detect and preload Nuxt-generated fonts

    // Function to scan for font files in .nuxt/dist directory
    const scanForFontFiles = async () => {
        try {
            const fontFiles = []
            const nuxtDistPath = join(process.cwd(), '.nuxt/dist')

            // Scan _nuxt directory for font files
            try {
                const nuxtFiles = await readdir(join(nuxtDistPath, '_nuxt'))

                for (const file of nuxtFiles) {
                    // Look for Montserrat font files
                    if (
                        file.includes('Montserrat') &&
                        file.endsWith('.woff2')
                    ) {
                        fontFiles.push(`/_nuxt/${file}`)
                        console.log('🔤 [Server] Found Montserrat font:', file)
                    }
                    // Also look for other woff2 files that might be fonts
                    else if (file.endsWith('.woff2')) {
                        fontFiles.push(`/_nuxt/${file}`)
                        console.log('🔤 [Server] Found font file:', file)
                    }
                }
            } catch (e) {
                console.log(
                    '🔤 [Server] _nuxt directory not found, will use runtime detection'
                )
            }

            return fontFiles
        } catch (error) {
            console.log(
                '🔤 [Server] Font scanning failed, will use runtime detection:',
                error.message
            )
            return []
        }
    }

    // Add font preloads to HTML head
    nuxtApp.hook('render:html', async (html, { event }) => {
        try {
            // Scan for font files
            const fontFiles = await scanForFontFiles()

            if (fontFiles.length > 0) {
                // Create preload links for detected fonts
                const fontPreloads = fontFiles
                    .map(
                        (fontPath) =>
                            `<link rel="preload" as="font" type="font/woff2" href="${fontPath}" crossorigin="anonymous" fetchpriority="high" data-dynamic-font="server">`
                    )
                    .join('\n    ')

                // Add to HTML head
                html.head.unshift(`
    <!-- Dynamic Font Preloads - Auto-detected -->
    ${fontPreloads}`)

                console.log(
                    `🔤 [Server] Added ${fontFiles.length} font preloads to HTML`
                )
            } else {
                console.log(
                    '🔤 [Server] No fonts detected, adding fallback font optimization'
                )

                // Add fallback font optimization
                html.head.unshift(`
    <!-- Font Optimization Fallback -->
    <style data-font-fallback="server">
        /* Optimize font loading with fallback */
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-display: swap;
        }
        
        /* When Montserrat loads, it will replace the fallback */
        .font-montserrat {
            font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-display: swap;
        }
    </style>`)
            }
        } catch (error) {
            console.error('🔤 [Server] Font preload injection failed:', error)
        }
    })

    console.log('🔤 [Server] Dynamic font preloader initialized')
})
