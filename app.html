<!DOCTYPE html>
<html {{ HTML_ATTRS }}>
  <head {{ HEAD_ATTRS }}>
    <!-- Critical Performance Optimizations -->
    <link rel="preconnect" href="https://fonts.googleapis.com" crossorigin>
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="dns-prefetch" href="//www.googletagmanager.com">
    <link rel="dns-prefetch" href="//connect.facebook.net">

    <!-- LCP Image Preloads -->
    <link rel="preload" as="image" type="image/avif" href="{{ $config.public.staticUrl }}/home/<USER>/jackpot/pc.avif" media="(min-width: 992px)" fetchpriority="high">
    <link rel="preload" as="image" type="image/avif" href="{{ $config.public.staticUrl }}/home/<USER>/jackpot/mb.avif" media="(max-width: 991px)" fetchpriority="high">

    <!-- Critical CSS -->
    <style data-critical="app">
      .hero-banner {
        position: relative;
        width: 100%;
        aspect-ratio: 1920/1080;
        contain: layout style paint;
      }

      @media (max-width: 991px) {
        .hero-banner {
          aspect-ratio: 430/195;
        }
      }

      .hero-banner img {
        width: 100%;
        height: auto;
        display: block;
        object-fit: cover;
        opacity: 1 !important;
        visibility: visible !important;
      }

      .providers-container {
        height: 3.375rem;
        contain: layout;
      }

      @media (min-width: 1024px) {
        .providers-container {
          height: 6rem;
        }
      }

      body {
        font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        font-display: swap;
      }
    </style>

    <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover">
    <meta name="format-detection" content="telephone=no">
    <meta name="theme-color" content="#000000">

    {{ HEAD }}
  </head>
  <body {{ BODY_ATTRS }}>
    {{ APP }}
  </body>
</html>
