<!DOCTYPE html>
<html {{ HTML_ATTRS }}>
  <head {{ HEAD_ATTRS }}>
    <!-- Dynamic Font Preloads - Auto-detected by server plugin -->
    <!-- Font files will be automatically detected and injected by plugins/dynamic-font-preloader.server.ts -->
    
    <!-- Critical Network Connections -->
    <link rel="preconnect" href="https://fonts.googleapis.com" crossorigin>
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="dns-prefetch" href="//www.googletagmanager.com">
    <link rel="dns-prefetch" href="//connect.facebook.net">
    
    <!-- LCP Image Preload Links -->
    <link rel="preload" as="image" type="image/avif" href="{{ $config.public.staticUrl }}/home/<USER>/jackpot/pc.avif" media="(min-width: 992px)" fetchpriority="high">
    <link rel="preload" as="image" type="image/avif" href="{{ $config.public.staticUrl }}/home/<USER>/jackpot/mb.avif" media="(max-width: 991px)" fetchpriority="high">
    
    <!-- Critical CSS for Performance -->
    <style data-critical="app">
      /* Critical CSS for above-the-fold content */
      .hero-banner {
        position: relative;
        width: 100%;
        aspect-ratio: 1920/1080;
        contain: layout style paint;
      }
      
      @media (max-width: 991px) {
        .hero-banner {
          aspect-ratio: 430/195;
        }
      }
      
      .hero-banner img {
        width: 100%;
        height: auto;
        display: block;
        object-fit: cover;
        opacity: 1 !important;
        visibility: visible !important;
      }
      
      /* Prevent layout shift for providers */
      .providers-container {
        height: 3.375rem;
        contain: layout;
      }
      
      @media (min-width: 1024px) {
        .providers-container {
          height: 6rem;
        }
      }
      
      /* Font loading optimization with fallback */
      body {
        font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        font-display: swap;
      }
      
      /* Prevent FOUC */
      .optimized-hero-img {
        opacity: 1 !important;
        visibility: visible !important;
      }
      
      /* Hide non-critical content initially */
      .providers-container img {
        opacity: 0;
        transition: opacity 0.3s ease-in-out;
      }
      
      .providers-container img[loading="lazy"] {
        opacity: 1;
      }
    </style>
    
    <!-- Performance optimization meta tags -->
    <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover">
    <meta name="format-detection" content="telephone=no">
    <meta name="theme-color" content="#000000">
    
    {{ HEAD }}
  </head>
  <body {{ BODY_ATTRS }}>
    {{ APP }}
    
    <!-- Reveal provider images after page load -->
    <script>
      document.addEventListener('DOMContentLoaded', function() {
        setTimeout(function() {
          const providerImages = document.querySelectorAll('.providers-container img');
          providerImages.forEach(function(img) {
            img.style.opacity = '1';
          });
        }, 500);
      });
    </script>
  </body>
</html>
